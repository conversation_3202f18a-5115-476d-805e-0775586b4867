using System;
using System.Threading.Tasks;
using Microsoft.Win32;
using NetworkResetter.Common.Interfaces;
using NetworkResetter.Models;

namespace NetworkResetter.Services
{
    public class ProxyService : IProxyService
    {
        private const string ProxyRegistryPath = @"Software\Microsoft\Windows\CurrentVersion\Internet Settings";
        private readonly ILoggingService _logger;

        public ProxyService(ILoggingService logger)
        {
            _logger = logger;
        }

        public async Task<ProxyConfig> GetCurrentProxyConfigAsync()
        {
            return await Task.Run(async () =>
            {
                try
                {
                    using var key = Registry.CurrentUser.OpenSubKey(ProxyRegistryPath);
                    if (key == null) return new ProxyConfig();

                    return new ProxyConfig
                    {
                        ProxyEnabled = Convert.ToBoolean(key.GetValue("ProxyEnable", 0)),
                        ProxyServer = key.GetValue("ProxyServer", "")?.ToString() ?? "",
                        ProxyBypass = key.GetValue("ProxyOverride", "")?.ToString() ?? "",
                        BackupTime = DateTime.Now
                    };
                }
                catch (Exception ex)
                {
                    await _logger.LogErrorAsync("获取代理配置失败", ex);
                    return new ProxyConfig();
                }
            });
        }

        public async Task<OperationResult> ResetProxySettingsAsync()
        {
            return await Task.Run(async () =>
            {
                try
                {
                    await _logger.LogInfoAsync("开始重置代理设置");
                    
                    using var key = Registry.CurrentUser.OpenSubKey(ProxyRegistryPath, true);
                    if (key == null) 
                    {
                        await _logger.LogWarningAsync("无法打开代理注册表项");
                        return OperationResult.CreateFailure("无法访问代理注册表设置", "REGISTRY_ACCESS_DENIED");
                    }

                    // 备份当前设置
                    var currentConfig = await GetCurrentProxyConfigAsync();
                    
                    key.SetValue("ProxyEnable", 0, RegistryValueKind.DWord);
                    key.SetValue("ProxyServer", "", RegistryValueKind.String);
                    key.SetValue("ProxyOverride", "", RegistryValueKind.String);

                    // 刷新系统设置
                    var refreshResult1 = InternetSetOption(IntPtr.Zero, INTERNET_OPTION_SETTINGS_CHANGED, IntPtr.Zero, 0);
                    var refreshResult2 = InternetSetOption(IntPtr.Zero, INTERNET_OPTION_REFRESH, IntPtr.Zero, 0);

                    if (!refreshResult1 || !refreshResult2)
                    {
                        await _logger.LogWarningAsync("系统设置刷新可能未完全成功");
                    }

                    await _logger.LogInfoAsync("代理设置重置完成");
                    
                    var result = OperationResult.CreateSuccess("代理设置重置完成");
                    if (currentConfig.ProxyEnabled)
                    {
                        result.AddDetail($"已清除代理服务器: {currentConfig.ProxyServer}");
                    }
                    result.AddDetail("系统代理已禁用");
                    
                    return result;
                }
                catch (UnauthorizedAccessException ex)
                {
                    await _logger.LogErrorAsync("重置代理设置权限不足", ex);
                    return OperationResult.CreateFailure("权限不足，无法修改代理设置", "ACCESS_DENIED", ex);
                }
                catch (Exception ex)
                {
                    await _logger.LogErrorAsync("重置代理设置失败", ex);
                    return OperationResult.CreateFailure("重置代理设置时发生异常", "PROXY_RESET_EXCEPTION", ex);
                }
            });
        }

        public async Task<OperationResult> RestoreProxySettingsAsync(ProxyConfig config)
        {
            return await Task.Run(async () =>
            {
                try
                {
                    if (config == null)
                    {
                        return OperationResult.CreateFailure("代理配置为空", "NULL_CONFIG");
                    }

                    await _logger.LogInfoAsync("开始恢复代理设置");
                    
                    using var key = Registry.CurrentUser.OpenSubKey(ProxyRegistryPath, true);
                    if (key == null) 
                    {
                        await _logger.LogWarningAsync("无法打开代理注册表项");
                        return OperationResult.CreateFailure("无法访问代理注册表设置", "REGISTRY_ACCESS_DENIED");
                    }

                    key.SetValue("ProxyEnable", config.ProxyEnabled ? 1 : 0, RegistryValueKind.DWord);
                    key.SetValue("ProxyServer", config.ProxyServer ?? "", RegistryValueKind.String);
                    key.SetValue("ProxyOverride", config.ProxyBypass ?? "", RegistryValueKind.String);

                    InternetSetOption(IntPtr.Zero, INTERNET_OPTION_SETTINGS_CHANGED, IntPtr.Zero, 0);
                    InternetSetOption(IntPtr.Zero, INTERNET_OPTION_REFRESH, IntPtr.Zero, 0);

                    await _logger.LogInfoAsync("代理设置恢复完成");
                    
                    var result = OperationResult.CreateSuccess("代理设置恢复完成");
                    result.AddDetail($"代理状态: {(config.ProxyEnabled ? "启用" : "禁用")}");
                    if (config.ProxyEnabled && !string.IsNullOrEmpty(config.ProxyServer))
                    {
                        result.AddDetail($"代理服务器: {config.ProxyServer}");
                    }
                    
                    return result;
                }
                catch (UnauthorizedAccessException ex)
                {
                    await _logger.LogErrorAsync("恢复代理设置权限不足", ex);
                    return OperationResult.CreateFailure("权限不足，无法修改代理设置", "ACCESS_DENIED", ex);
                }
                catch (Exception ex)
                {
                    await _logger.LogErrorAsync("恢复代理设置失败", ex);
                    return OperationResult.CreateFailure("恢复代理设置时发生异常", "PROXY_RESTORE_EXCEPTION", ex);
                }
            });
        }

        // Windows API 声明
        [System.Runtime.InteropServices.DllImport("wininet.dll")]
        private static extern bool InternetSetOption(IntPtr hInternet, int dwOption, IntPtr lpBuffer, int dwBufferLength);

        private const int INTERNET_OPTION_SETTINGS_CHANGED = 39;
        private const int INTERNET_OPTION_REFRESH = 37;
    }
}