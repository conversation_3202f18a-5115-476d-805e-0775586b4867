<Window x:Class="NetworkResetter.Views.NetworkConfigWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:local="clr-namespace:NetworkResetter.Views"
        Title="网络配置详情" Height="600" Width="800"
        WindowStartupLocation="CenterOwner">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" Text="当前网络配置" 
                   FontSize="18" FontWeight="Bold" 
                   Margin="0,0,0,15"/>

        <!-- 配置列表 -->
        <ScrollViewer Grid.Row="1">
            <ItemsControl x:Name="ConfigItemsControl">
                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <GroupBox Header="{Binding AdapterName}" Margin="0,0,0,15">
                            <Grid Margin="10">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="120"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="IP地址:" FontWeight="Bold"/>
                                <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding IpAddress}" Margin="5,0,0,0"/>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="子网掩码:" FontWeight="Bold"/>
                                <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding SubnetMask}" Margin="5,0,0,0"/>

                                <TextBlock Grid.Row="2" Grid.Column="0" Text="默认网关:" FontWeight="Bold"/>
                                <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding Gateway}" Margin="5,0,0,0"/>

                                <TextBlock Grid.Row="3" Grid.Column="0" Text="MAC地址:" FontWeight="Bold"/>
                                <TextBlock Grid.Row="3" Grid.Column="1" Text="{Binding MacAddress}" Margin="5,0,0,0"/>

                                <TextBlock Grid.Row="4" Grid.Column="0" Text="DHCP:" FontWeight="Bold"/>
                                <TextBlock Grid.Row="4" Grid.Column="1" Text="{Binding DhcpEnabled, Converter={StaticResource BoolToYesNoConverter}}" Margin="5,0,0,0"/>

                                <TextBlock Grid.Row="5" Grid.Column="0" Text="DHCP服务器:" FontWeight="Bold"/>
                                <TextBlock Grid.Row="5" Grid.Column="1" Text="{Binding DhcpServer}" Margin="5,0,0,0"/>

                                <TextBlock Grid.Row="6" Grid.Column="0" Text="DNS服务器:" FontWeight="Bold"/>
                                <ItemsControl Grid.Row="6" Grid.Column="1" ItemsSource="{Binding DnsServers}" Margin="5,0,0,0">
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <TextBlock Text="{Binding}" Margin="0,0,0,2"/>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>

                                <TextBlock Grid.Row="7" Grid.Column="0" Text="备份时间:" FontWeight="Bold"/>
                                <TextBlock Grid.Row="7" Grid.Column="1" Text="{Binding BackupTime, StringFormat=yyyy-MM-dd HH:mm:ss}" Margin="5,0,0,0"/>
                            </Grid>
                        </GroupBox>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </ScrollViewer>

        <!-- 按钮 -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" 
                    HorizontalAlignment="Right" Margin="0,15,0,0">
            <Button Content="刷新" Click="Refresh_Click" 
                    Width="80" Margin="0,0,10,0"/>
            <Button Content="导出" Click="Export_Click" 
                    Width="80" Margin="0,0,10,0"/>
            <Button Content="关闭" Click="Close_Click" 
                    Width="80" IsDefault="True"/>
        </StackPanel>
    </Grid>

    <Window.Resources>
        <local:BoolToYesNoConverter x:Key="BoolToYesNoConverter"/>
    </Window.Resources>
</Window>