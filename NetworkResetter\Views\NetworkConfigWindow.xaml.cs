using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Data;
using Microsoft.Win32;
using NetworkResetter.Common.Interfaces;
using NetworkResetter.Models;

namespace NetworkResetter.Views
{
    public partial class NetworkConfigWindow : Window
    {
        private readonly INetworkService _networkService;
        private readonly ILoggingService _logger;
        private List<NetworkConfig> _currentConfigs;

        public NetworkConfigWindow(INetworkService networkService, ILoggingService logger)
        {
            InitializeComponent();
            _networkService = networkService;
            _logger = logger;
            
            _ = LoadConfigsAsync();
        }

        private async Task LoadConfigsAsync()
        {
            try
            {
                _currentConfigs = await _networkService.GetNetworkConfigsAsync();
                ConfigItemsControl.ItemsSource = _currentConfigs;
                
                await _logger.LogInfoAsync($"网络配置窗口加载了 {_currentConfigs.Count} 个配置");
            }
            catch (Exception ex)
            {
                await _logger.LogErrorAsync("加载网络配置失败", ex);
                MessageBox.Show($"加载网络配置失败: {ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void Refresh_Click(object sender, RoutedEventArgs e)
        {
            await LoadConfigsAsync();
        }

        private async void Export_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "JSON文件|*.json|文本文件|*.txt",
                    DefaultExt = "json",
                    FileName = $"network_config_{DateTime.Now:yyyyMMdd_HHmmss}"
                };

                if (saveDialog.ShowDialog() == true)
                {
                    var options = new JsonSerializerOptions 
                    { 
                        WriteIndented = true,
                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                    };
                    
                    var json = JsonSerializer.Serialize(_currentConfigs, options);
                    await File.WriteAllTextAsync(saveDialog.FileName, json);
                    
                    MessageBox.Show("网络配置导出成功", "导出完成", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    
                    await _logger.LogInfoAsync($"网络配置导出到: {saveDialog.FileName}");
                }
            }
            catch (Exception ex)
            {
                await _logger.LogErrorAsync("导出网络配置失败", ex);
                MessageBox.Show($"导出失败: {ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void Close_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }

    public class BoolToYesNoConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
                return boolValue ? "是" : "否";
            return "未知";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}