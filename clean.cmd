@echo off
chcp 65001 >nul
echo NetworkResetter - Clean Script
echo =================================

echo Cleaning build outputs...
if exist "NetworkResetter\bin" rmdir /s /q "NetworkResetter\bin"
if exist "NetworkResetter\obj" rmdir /s /q "NetworkResetter\obj"
if exist "NetworkResetter.Tests\bin" rmdir /s /q "NetworkResetter.Tests\bin"
if exist "NetworkResetter.Tests\obj" rmdir /s /q "NetworkResetter.Tests\obj"

echo Cleaning Visual Studio files...
if exist ".vs" rmdir /s /q ".vs"

echo Cleaning publish outputs...
if exist "publish" rmdir /s /q "publish"

echo Cleaning log files...
if exist "*.log" del /q "*.log"
if exist "reset.log" del /q "reset.log"

echo Cleaning NuGet cache...
dotnet nuget locals all --clear

echo.
echo =================================
echo Clean completed successfully!
echo =================================