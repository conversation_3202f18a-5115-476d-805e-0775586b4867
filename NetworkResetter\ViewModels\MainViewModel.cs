using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows.Input;
using System.Windows;
using NetworkResetter.Common.Interfaces;
using NetworkResetter.Common.UI;
using NetworkResetter.Models;
using NetworkResetter.Services;

namespace NetworkResetter.ViewModels
{
    public class MainViewModel : INotifyPropertyChanged, IDisposable
    {
        private readonly INetworkService _networkService;
        private readonly IProxyService _proxyService;
        private readonly IBackupService _backupService;
        private readonly IFirewallService _firewallService;
        private readonly ILoggingService _logger;
        private readonly HealthCheckService _healthCheckService;

        private string _statusMessage = "就绪";
        private bool _isOperationInProgress;
        private NetworkDiagnostics _diagnostics;
        private string _selectedBackup;
        private string _operationLog = "";
        private bool _disposed;

        public MainViewModel(
            INetworkService networkService,
            IProxyService proxyService,
            IBackupService backupService,
            IFirewallService firewallService,
            ILoggingService logger,
            HealthCheckService healthCheckService)
        {
            _networkService = networkService;
            _proxyService = proxyService;
            _backupService = backupService;
            _firewallService = firewallService;
            _logger = logger;
            _healthCheckService = healthCheckService;

            InitializeCommands();
            AvailableBackups = new ObservableCollection<string>();
            
            _ = InitializeAsync();
        }

        private void InitializeCommands()
        {
            ResetTcpIpCommand = new RelayCommand(async () => await ResetTcpIpAsync(), () => !IsOperationInProgress);
            ResetAdaptersCommand = new RelayCommand(async () => await ResetAdaptersAsync(), () => !IsOperationInProgress);
            ResetProxyCommand = new RelayCommand(async () => await ResetProxyAsync(), () => !IsOperationInProgress);
            ResetFirewallCommand = new RelayCommand(async () => await ResetFirewallAsync(), () => !IsOperationInProgress);
            RunDiagnosticsCommand = new RelayCommand(async () => await RunDiagnosticsAsync(), () => !IsOperationInProgress);
            CreateBackupCommand = new RelayCommand(async () => await CreateBackupAsync(), () => !IsOperationInProgress);
            RestoreBackupCommand = new RelayCommand<string>(async (backup) => await RestoreBackupAsync(backup), (backup) => !IsOperationInProgress && !string.IsNullOrEmpty(backup));
            ResetAllCommand = new RelayCommand(async () => await ResetAllAsync(), () => !IsOperationInProgress);
            ViewLogsCommand = new RelayCommand(async () => await ViewLogsAsync());
            ShowHelpCommand = new RelayCommand(async () => await ShowHelpAsync());
            ViewNetworkConfigCommand = new RelayCommand(async () => await ViewNetworkConfigAsync());
            RunHealthCheckCommand = new RelayCommand(async () => await RunHealthCheckAsync());
            ShowPerformanceMonitorCommand = new RelayCommand(async () => await ShowPerformanceMonitorAsync());
            ShowSettingsCommand = new RelayCommand(async () => await ShowSettingsAsync());
        }

        private async Task InitializeAsync()
        {
            try
            {
                await _logger.LogInfoAsync("MainViewModel 初始化");
                await LoadBackupsAsync();
            }
            catch (Exception ex)
            {
                await _logger.LogErrorAsync("MainViewModel 初始化失败", ex);
            }
        }

        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        public bool IsOperationInProgress
        {
            get => _isOperationInProgress;
            set => SetProperty(ref _isOperationInProgress, value);
        }

        public NetworkDiagnostics Diagnostics
        {
            get => _diagnostics;
            set => SetProperty(ref _diagnostics, value);
        }

        public string SelectedBackup
        {
            get => _selectedBackup;
            set => SetProperty(ref _selectedBackup, value);
        }

        public string OperationLog
        {
            get => _operationLog;
            set => SetProperty(ref _operationLog, value);
        }

        public ObservableCollection<string> AvailableBackups { get; }

        public ICommand ResetTcpIpCommand { get; private set; }
        public ICommand ResetAdaptersCommand { get; private set; }
        public ICommand ResetProxyCommand { get; private set; }
        public ICommand ResetFirewallCommand { get; private set; }
        public ICommand RunDiagnosticsCommand { get; private set; }
        public ICommand CreateBackupCommand { get; private set; }
        public ICommand RestoreBackupCommand { get; private set; }
        public ICommand ResetAllCommand { get; private set; }
        public ICommand ViewLogsCommand { get; private set; }
        public ICommand ShowHelpCommand { get; private set; }
        public ICommand ViewNetworkConfigCommand { get; private set; }
        public ICommand RunHealthCheckCommand { get; private set; }
        public ICommand ShowPerformanceMonitorCommand { get; private set; }
        public ICommand ShowSettingsCommand { get; private set; }

        private async Task ResetTcpIpAsync()
        {
            if (!await ConfirmOperationAsync("重置TCP/IP栈", "此操作将重置网络协议栈，可能需要重启计算机。是否继续？"))
                return;

            IsOperationInProgress = true;
            StatusMessage = "正在重置TCP/IP栈...";

            try
            {
                var result = await _networkService.ResetTcpIpStackAsync();
                StatusMessage = result.Success ? 
                    "TCP/IP栈重置完成，建议重启计算机" : 
                    $"TCP/IP栈重置失败: {result.Message}";
                
                await _logger.LogInfoAsync($"TCP/IP栈重置结果: {result.Message}");
            }
            catch (Exception ex)
            {
                StatusMessage = "TCP/IP栈重置异常";
                await _logger.LogErrorAsync("TCP/IP栈重置异常", ex);
            }
            finally
            {
                IsOperationInProgress = false;
            }
        }

        private async Task ResetAdaptersAsync()
        {
            if (!await ConfirmOperationAsync("重置网络适配器", "此操作将禁用并重新启用所有网络适配器，可能会短暂中断网络连接。是否继续？"))
                return;

            IsOperationInProgress = true;
            StatusMessage = "正在重置网络适配器...";

            try
            {
                var result = await _networkService.ResetNetworkAdaptersAsync();
                StatusMessage = result.Success ? 
                    "网络适配器重置完成" : 
                    $"网络适配器重置失败: {result.Message}";
                
                await _logger.LogInfoAsync($"网络适配器重置结果: {result.Message}");
            }
            catch (Exception ex)
            {
                StatusMessage = "网络适配器重置异常";
                await _logger.LogErrorAsync("网络适配器重置异常", ex);
            }
            finally
            {
                IsOperationInProgress = false;
            }
        }

        private async Task ResetProxyAsync()
        {
            IsOperationInProgress = true;
            StatusMessage = "正在重置代理设置...";

            try
            {
                var result = await _proxyService.ResetProxySettingsAsync();
                StatusMessage = result.Success ? 
                    "代理设置重置完成" : 
                    $"代理设置重置失败: {result.Message}";
                
                await _logger.LogInfoAsync($"代理设置重置结果: {result.Message}");
            }
            catch (Exception ex)
            {
                StatusMessage = "代理设置重置异常";
                await _logger.LogErrorAsync("代理设置重置异常", ex);
            }
            finally
            {
                IsOperationInProgress = false;
            }
        }

        private async Task ResetFirewallAsync()
        {
            if (!await ConfirmOperationAsync("重置防火墙", "此操作将重置Windows防火墙到默认设置。是否继续？"))
                return;

            IsOperationInProgress = true;
            StatusMessage = "正在重置防火墙设置...";

            try
            {
                var result = await _firewallService.ResetFirewallToDefaultAsync();
                StatusMessage = result.Success ? 
                    "防火墙重置完成" : 
                    $"防火墙重置失败: {result.Message}";
                
                await _logger.LogInfoAsync($"防火墙重置结果: {result.Message}");
            }
            catch (Exception ex)
            {
                StatusMessage = "防火墙重置异常";
                await _logger.LogErrorAsync("防火墙重置异常", ex);
            }
            finally
            {
                IsOperationInProgress = false;
            }
        }

        private async Task RunDiagnosticsAsync()
        {
            IsOperationInProgress = true;
            StatusMessage = "正在运行网络诊断...";

            Diagnostics = await _networkService.RunDiagnosticsAsync();
            StatusMessage = "网络诊断完成";

            IsOperationInProgress = false;
        }

        private async Task CreateBackupAsync()
        {
            IsOperationInProgress = true;
            StatusMessage = "正在创建备份...";

            try
            {
                var result = await _backupService.CreateBackupAsync();
                StatusMessage = result.Success ? 
                    "备份创建成功" : 
                    $"备份创建失败: {result.Message}";
                
                await _logger.LogInfoAsync($"备份创建结果: {result.Message}");
                
                if (result.Success)
                {
                    await LoadBackupsAsync();
                }
            }
            catch (Exception ex)
            {
                StatusMessage = "备份创建异常";
                await _logger.LogErrorAsync("备份创建异常", ex);
            }
            finally
            {
                IsOperationInProgress = false;
            }
        }

        private async Task RestoreBackupAsync(string backup)
        {
            if (string.IsNullOrEmpty(backup)) return;

            if (!await ConfirmOperationAsync("恢复备份", $"此操作将恢复选中的备份配置：{backup}。是否继续？"))
                return;

            IsOperationInProgress = true;
            StatusMessage = "正在恢复备份...";

            try
            {
                var result = await _backupService.RestoreBackupAsync(backup);
                StatusMessage = result.Success ? 
                    "备份恢复成功" : 
                    $"备份恢复失败: {result.Message}";
                
                await _logger.LogInfoAsync($"备份恢复结果: {result.Message}");
            }
            catch (Exception ex)
            {
                StatusMessage = "备份恢复异常";
                await _logger.LogErrorAsync("备份恢复异常", ex);
            }
            finally
            {
                IsOperationInProgress = false;
            }
        }

        private async Task ResetAllAsync()
        {
            if (!await ConfirmOperationAsync("完全重置", "此操作将执行所有重置功能，包括TCP/IP栈、网络适配器、代理设置和防火墙。操作完成后建议重启计算机。是否继续？"))
                return;

            IsOperationInProgress = true;
            var successCount = 0;
            var totalOperations = 5;

            try
            {
                // 创建备份
                StatusMessage = "正在创建备份...";
                var backupResult = await _backupService.CreateBackupAsync();
                if (backupResult.Success) successCount++;

                // 重置代理
                StatusMessage = "正在重置代理设置...";
                var proxyResult = await _proxyService.ResetProxySettingsAsync();
                if (proxyResult.Success) successCount++;

                // 重置防火墙
                StatusMessage = "正在重置防火墙...";
                var firewallResult = await _firewallService.ResetFirewallToDefaultAsync();
                if (firewallResult.Success) successCount++;

                // 重置网络适配器
                StatusMessage = "正在重置网络适配器...";
                var adapterResult = await _networkService.ResetNetworkAdaptersAsync();
                if (adapterResult.Success) successCount++;

                // 重置TCP/IP栈
                StatusMessage = "正在重置TCP/IP栈...";
                var tcpipResult = await _networkService.ResetTcpIpStackAsync();
                if (tcpipResult.Success) successCount++;

                StatusMessage = successCount == totalOperations ? 
                    "完全重置完成，建议重启计算机" : 
                    $"完全重置部分完成 ({successCount}/{totalOperations})，建议重启计算机";

                await _logger.LogInfoAsync($"完全重置操作完成，成功: {successCount}/{totalOperations}");
            }
            catch (Exception ex)
            {
                StatusMessage = "完全重置异常";
                await _logger.LogErrorAsync("完全重置异常", ex);
            }
            finally
            {
                IsOperationInProgress = false;
            }
        }

        private async Task ViewLogsAsync()
        {
            OperationLog = await _logger.GetTodayLogsAsync();
        }

        private Task ShowHelpAsync()
        {
            var helpWindow = new Views.HelpWindow();
            helpWindow.ShowDialog();
            
            return Task.CompletedTask;
        }

        private Task ViewNetworkConfigAsync()
        {
            var configWindow = new Views.NetworkConfigWindow(_networkService, _logger);
            configWindow.ShowDialog();
            
            return Task.CompletedTask;
        }

        private async Task RunHealthCheckAsync()
        {
            IsOperationInProgress = true;
            StatusMessage = "正在运行系统健康检查...";

            try
            {
                var result = await _healthCheckService.RunHealthCheckAsync();
                StatusMessage = $"健康检查完成: {result.Summary}";
                
                await NotificationService.ShowOperationResultAsync(
                    OperationResult.CreateSuccess(result.Summary)
                        .AddDetail($"检查时间: {result.CheckTime:yyyy-MM-dd HH:mm:ss}")
                        .AddDetail($"总体状态: {result.OverallStatus}"),
                    "系统健康检查");
                
                await _logger.LogInfoAsync($"健康检查结果: {result.Summary}");
            }
            catch (Exception ex)
            {
                StatusMessage = "健康检查异常";
                await _logger.LogErrorAsync("健康检查异常", ex);
                await NotificationService.ShowErrorAsync("健康检查失败", ex.Message, ex);
            }
            finally
            {
                IsOperationInProgress = false;
            }
        }

        private Task ShowPerformanceMonitorAsync()
        {
            var performanceWindow = new Views.PerformanceMonitorWindow(_logger);
            performanceWindow.ShowDialog();
            
            return Task.CompletedTask;
        }

        private Task ShowSettingsAsync()
        {
            var settingsWindow = new Views.SettingsWindow(_logger);
            if (settingsWindow.ShowDialog() == true)
            {
                // 设置已保存，可能需要重新加载某些配置
                StatusMessage = "设置已更新";
            }
            
            return Task.CompletedTask;
        }

        private async Task LoadBackupsAsync()
        {
            var backups = await _backupService.GetAvailableBackupsAsync();
            AvailableBackups.Clear();
            foreach (var backup in backups)
            {
                AvailableBackups.Add(backup);
            }
        }

        private async Task<bool> ConfirmOperationAsync(string title, string message)
        {
            return await NotificationService.ShowConfirmationAsync(title, message);
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string propertyName = null)
        {
            if (Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (_disposed) return;

            if (disposing)
            {
                try
                {
                    // 取消所有正在进行的操作
                    if (IsOperationInProgress)
                    {
                        _logger?.LogInfoAsync("MainViewModel 正在释放资源，取消正在进行的操作").Wait(1000);
                    }

                    _logger?.LogInfoAsync("MainViewModel 正在释放资源").Wait(1000);
                    
                    // 不要释放注入的服务，它们由DI容器管理
                    // 只释放我们自己创建的资源
                }
                catch (Exception ex)
                {
                    // 记录释放异常但不抛出
                    System.Diagnostics.Debug.WriteLine($"MainViewModel Dispose error: {ex.Message}");
                }
            }
            
            _disposed = true;
        }

        ~MainViewModel()
        {
            Dispose(false);
        }
    }

    public class RelayCommand : ICommand
    {
        private readonly Func<Task> _execute;
        private readonly Func<bool> _canExecute;

        public RelayCommand(Func<Task> execute, Func<bool> canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }

        public event EventHandler CanExecuteChanged
        {
            add => System.Windows.Input.CommandManager.RequerySuggested += value;
            remove => System.Windows.Input.CommandManager.RequerySuggested -= value;
        }

        public bool CanExecute(object parameter) => _canExecute?.Invoke() ?? true;

        public async void Execute(object parameter) => await _execute();
    }

    public class RelayCommand<T> : ICommand
    {
        private readonly Func<T, Task> _execute;
        private readonly Func<T, bool> _canExecute;

        public RelayCommand(Func<T, Task> execute, Func<T, bool> canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }

        public event EventHandler CanExecuteChanged
        {
            add => System.Windows.Input.CommandManager.RequerySuggested += value;
            remove => System.Windows.Input.CommandManager.RequerySuggested -= value;
        }

        public bool CanExecute(object parameter) => _canExecute?.Invoke((T)parameter) ?? true;

        public async void Execute(object parameter) => await _execute((T)parameter);
    }
}