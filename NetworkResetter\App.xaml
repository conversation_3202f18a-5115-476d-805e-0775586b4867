<Application x:Class="NetworkResetter.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <Application.Resources>
        <ResourceDictionary>
            <!-- 颜色定义 -->
            <SolidColorBrush x:Key="PrimaryBrush" Color="#FF0078D4"/>
            <SolidColorBrush x:Key="PrimaryHoverBrush" Color="#FF106EBE"/>
            <SolidColorBrush x:Key="DangerBrush" Color="#FFDC3545"/>
            <SolidColorBrush x:Key="DangerHoverBrush" Color="#FFC82333"/>
            <SolidColorBrush x:Key="SuccessBrush" Color="#FF28A745"/>
            <SolidColorBrush x:Key="WarningBrush" Color="#FFFFC107"/>
            <SolidColorBrush x:Key="BackgroundBrush" Color="#FFF8F9FA"/>
            <SolidColorBrush x:Key="BorderBrush" Color="#FFDEE2E6"/>
            <SolidColorBrush x:Key="TextBrush" Color="#FF212529"/>
            <SolidColorBrush x:Key="MutedTextBrush" Color="#FF6C757D"/>

            <!-- 按钮样式 -->
            <Style TargetType="Button">
                <Setter Property="Margin" Value="5"/>
                <Setter Property="Padding" Value="15,8"/>
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="FontWeight" Value="Medium"/>
                <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
                <Setter Property="Foreground" Value="White"/>
                <Setter Property="BorderThickness" Value="0"/>
                <Setter Property="Cursor" Value="Hand"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}" 
                                    CornerRadius="4" 
                                    Padding="{TemplateBinding Padding}">
                                <ContentPresenter HorizontalAlignment="Center" 
                                                VerticalAlignment="Center"/>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="{StaticResource PrimaryHoverBrush}"/>
                                </Trigger>
                                <Trigger Property="IsEnabled" Value="False">
                                    <Setter Property="Background" Value="#FFCCCCCC"/>
                                    <Setter Property="Foreground" Value="#FF666666"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <!-- 危险按钮样式 -->
            <Style x:Key="DangerButtonStyle" TargetType="Button" BasedOn="{StaticResource {x:Type Button}}">
                <Setter Property="Background" Value="{StaticResource DangerBrush}"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}" 
                                    CornerRadius="4" 
                                    Padding="{TemplateBinding Padding}">
                                <ContentPresenter HorizontalAlignment="Center" 
                                                VerticalAlignment="Center"/>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="{StaticResource DangerHoverBrush}"/>
                                </Trigger>
                                <Trigger Property="IsEnabled" Value="False">
                                    <Setter Property="Background" Value="#FFCCCCCC"/>
                                    <Setter Property="Foreground" Value="#FF666666"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>
            
            <!-- GroupBox样式 -->
            <Style TargetType="GroupBox">
                <Setter Property="Margin" Value="5"/>
                <Setter Property="Padding" Value="15"/>
                <Setter Property="FontWeight" Value="SemiBold"/>
                <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="Background" Value="White"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="GroupBox">
                            <Grid>
                                <Border Background="{TemplateBinding Background}" 
                                        BorderBrush="{TemplateBinding BorderBrush}" 
                                        BorderThickness="{TemplateBinding BorderThickness}"
                                        CornerRadius="6">
                                    <ContentPresenter Margin="{TemplateBinding Padding}"/>
                                </Border>
                                <Label Content="{TemplateBinding Header}" 
                                       Background="White"
                                       Padding="8,2"
                                       Margin="15,-8,0,0"
                                       FontWeight="{TemplateBinding FontWeight}"
                                       VerticalAlignment="Top"
                                       HorizontalAlignment="Left"/>
                            </Grid>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>
            
            <!-- TextBlock样式 -->
            <Style TargetType="TextBlock">
                <Setter Property="Margin" Value="2"/>
                <Setter Property="VerticalAlignment" Value="Center"/>
                <Setter Property="Foreground" Value="{StaticResource TextBrush}"/>
            </Style>

            <!-- TabControl样式 -->
            <Style TargetType="TabControl">
                <Setter Property="Background" Value="{StaticResource BackgroundBrush}"/>
                <Setter Property="BorderThickness" Value="0"/>
            </Style>

            <!-- TabItem样式 -->
            <Style TargetType="TabItem">
                <Setter Property="Padding" Value="20,10"/>
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="FontWeight" Value="Medium"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="TabItem">
                            <Border Name="Border" 
                                    Background="White" 
                                    BorderBrush="{StaticResource BorderBrush}" 
                                    BorderThickness="1,1,1,0"
                                    CornerRadius="6,6,0,0"
                                    Margin="2,0">
                                <ContentPresenter x:Name="ContentSite"
                                                VerticalAlignment="Center"
                                                HorizontalAlignment="Center"
                                                ContentSource="Header"
                                                Margin="{TemplateBinding Padding}"/>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsSelected" Value="True">
                                    <Setter TargetName="Border" Property="Background" Value="White"/>
                                    <Setter TargetName="Border" Property="BorderThickness" Value="1,1,1,0"/>
                                </Trigger>
                                <Trigger Property="IsSelected" Value="False">
                                    <Setter TargetName="Border" Property="Background" Value="{StaticResource BackgroundBrush}"/>
                                    <Setter TargetName="Border" Property="BorderThickness" Value="1,1,1,1"/>
                                </Trigger>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter TargetName="Border" Property="Background" Value="#FFF0F0F0"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>
        </ResourceDictionary>
    </Application.Resources>
</Application>