using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using NetworkResetter.Models;

namespace NetworkResetter.Common.Interfaces
{
    public interface INetworkService
    {
        Task<OperationResult> ResetTcpIpStackAsync();
        Task<OperationResult> ResetNetworkAdaptersAsync();
        Task<NetworkDiagnostics> RunDiagnosticsAsync();
        Task<List<NetworkConfig>> GetNetworkConfigsAsync();
    }

    public interface IProxyService
    {
        Task<ProxyConfig> GetCurrentProxyConfigAsync();
        Task<OperationResult> ResetProxySettingsAsync();
        Task<OperationResult> RestoreProxySettingsAsync(ProxyConfig config);
    }

    public interface IFirewallService
    {
        Task<OperationResult> ResetFirewallToDefaultAsync();
        Task<OperationResult> EnableFirewallAsync();
        Task<OperationResult> DisableFirewallAsync();
    }

    public interface IBackupService
    {
        Task<OperationResult<string>> CreateBackupAsync();
        Task<List<string>> GetAvailableBackupsAsync();
        Task<OperationResult> RestoreBackupAsync(string backupFileName);
    }

    public interface ILoggingService
    {
        Task LogInfoAsync(string message);
        Task LogWarningAsync(string message);
        Task LogErrorAsync(string message, Exception exception = null);
        Task<string> GetTodayLogsAsync();
    }

    public interface ISystemCommandService
    {
        Task<CommandResult> ExecuteCommandAsync(string command, int timeoutMs = 30000);
        Task<CommandResult> ExecuteCommandSafeAsync(string command, string[] allowedCommands, int timeoutMs = 30000);
    }
}