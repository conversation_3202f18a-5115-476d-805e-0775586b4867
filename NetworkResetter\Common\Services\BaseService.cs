using System;
using System.Threading.Tasks;
using NetworkResetter.Common.Exceptions;
using NetworkResetter.Common.Interfaces;
using NetworkResetter.Models;

namespace NetworkResetter.Common.Services
{
    public abstract class BaseService
    {
        protected readonly ILoggingService _logger;

        protected BaseService(ILoggingService logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        protected async Task<OperationResult> ExecuteOperationAsync(Func<Task<OperationResult>> operation, string operationName)
        {
            try
            {
                await _logger.LogInfoAsync($"开始执行操作: {operationName}");
                var result = await operation();
                
                if (result.Success)
                {
                    await _logger.LogInfoAsync($"操作成功完成: {operationName}");
                }
                else
                {
                    await _logger.LogWarningAsync($"操作失败: {operationName} - {result.Message}");
                }
                
                return result;
            }
            catch (NetworkResetterException ex)
            {
                await _logger.LogErrorAsync($"操作异常: {operationName}", ex);
                return OperationResult.CreateFailure(ex.UserFriendlyMessage, ex.ErrorCode, ex);
            }
            catch (Exception ex)
            {
                await _logger.LogErrorAsync($"操作发生未预期异常: {operationName}", ex);
                return OperationResult.CreateFailure("操作执行过程中发生未预期错误，请查看日志获取详细信息", "UNEXPECTED_ERROR", ex);
            }
        }

        protected async Task<OperationResult<T>> ExecuteOperationAsync<T>(Func<Task<OperationResult<T>>> operation, string operationName)
        {
            try
            {
                await _logger.LogInfoAsync($"开始执行操作: {operationName}");
                var result = await operation();
                
                if (result.Success)
                {
                    await _logger.LogInfoAsync($"操作成功完成: {operationName}");
                }
                else
                {
                    await _logger.LogWarningAsync($"操作失败: {operationName} - {result.Message}");
                }
                
                return result;
            }
            catch (NetworkResetterException ex)
            {
                await _logger.LogErrorAsync($"操作异常: {operationName}", ex);
                return OperationResult<T>.CreateFailure(ex.UserFriendlyMessage, ex.ErrorCode, ex);
            }
            catch (Exception ex)
            {
                await _logger.LogErrorAsync($"操作发生未预期异常: {operationName}", ex);
                return OperationResult<T>.CreateFailure("操作执行过程中发生未预期错误，请查看日志获取详细信息", "UNEXPECTED_ERROR", ex);
            }
        }

        protected void ValidateNotNull(object value, string parameterName)
        {
            if (value == null)
                throw new ArgumentNullException(parameterName);
        }

        protected void ValidateNotNullOrEmpty(string value, string parameterName)
        {
            if (string.IsNullOrWhiteSpace(value))
                throw new ArgumentException($"参数 {parameterName} 不能为空", parameterName);
        }
    }
}