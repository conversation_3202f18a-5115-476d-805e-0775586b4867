using System;
using System.Collections.Concurrent;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using NetworkResetter.Common.Configuration;
using NetworkResetter.Common.Interfaces;

namespace NetworkResetter.Services
{
    public class LoggingService : ILoggingService, IDisposable
    {
        private readonly string _logDirectory;
        private readonly AppConfig _config;
        private readonly ConcurrentQueue<LogEntry> _logQueue;
        private readonly Timer _flushTimer;
        private readonly SemaphoreSlim _writeSemaphore;
        private readonly CancellationTokenSource _cancellationTokenSource;
        private bool _disposed;

        public LoggingService()
        {
            _config = AppConfig.Instance;
            _logDirectory = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "NetworkResetter", "Logs");
            Directory.CreateDirectory(_logDirectory);
            
            _logQueue = new ConcurrentQueue<LogEntry>();
            _writeSemaphore = new SemaphoreSlim(1, 1);
            _cancellationTokenSource = new CancellationTokenSource();
            
            // 每5秒刷新一次日志队列
            _flushTimer = new Timer(FlushLogs, null, TimeSpan.FromSeconds(5), TimeSpan.FromSeconds(5));
            
            // 清理旧日志文件
            _ = Task.Run(CleanupOldLogsAsync);
        }

        public async Task LogInfoAsync(string message)
        {
            await EnqueueLogAsync(LogLevel.Info, message);
        }

        public async Task LogWarningAsync(string message)
        {
            await EnqueueLogAsync(LogLevel.Warning, message);
        }

        public async Task LogErrorAsync(string message, Exception exception = null)
        {
            var fullMessage = exception != null ? $"{message}\n异常详情: {exception}" : message;
            await EnqueueLogAsync(LogLevel.Error, fullMessage);
        }

        private async Task EnqueueLogAsync(LogLevel level, string message)
        {
            if (_disposed) return;

            var logEntry = new LogEntry
            {
                Timestamp = DateTime.Now,
                Level = level,
                Message = message,
                ThreadId = Thread.CurrentThread.ManagedThreadId
            };

            _logQueue.Enqueue(logEntry);

            // 如果是错误日志，立即刷新
            if (level == LogLevel.Error)
            {
                await FlushLogsAsync();
            }
        }

        private async void FlushLogs(object state)
        {
            await FlushLogsAsync();
        }

        private async Task FlushLogsAsync()
        {
            if (_disposed || _logQueue.IsEmpty) return;

            await _writeSemaphore.WaitAsync(_cancellationTokenSource.Token);
            try
            {
                var logFilePath = GetCurrentLogFilePath();
                var logEntries = new System.Text.StringBuilder();

                while (_logQueue.TryDequeue(out var logEntry))
                {
                    var logLine = FormatLogEntry(logEntry);
                    logEntries.AppendLine(logLine);

                    // 控制台输出（如果启用）
                    if (_config.Logging.EnableConsoleLogging)
                    {
                        Console.WriteLine(logLine);
                    }
                }

                if (logEntries.Length > 0)
                {
                    await File.AppendAllTextAsync(logFilePath, logEntries.ToString(), _cancellationTokenSource.Token);
                    
                    // 检查文件大小，如果超过限制则轮转
                    await RotateLogFileIfNeededAsync(logFilePath);
                }
            }
            catch (OperationCanceledException)
            {
                // 正常取消，忽略
            }
            catch (Exception ex)
            {
                // 日志写入失败，尝试写入到备用位置
                await WriteToFallbackLogAsync($"日志写入失败: {ex.Message}");
            }
            finally
            {
                _writeSemaphore.Release();
            }
        }

        private string GetCurrentLogFilePath()
        {
            return Path.Combine(_logDirectory, $"log_{DateTime.Now:yyyyMMdd}.txt");
        }

        private string FormatLogEntry(LogEntry entry)
        {
            return $"[{entry.Timestamp:yyyy-MM-dd HH:mm:ss.fff}] [{entry.Level}] [T{entry.ThreadId}] {entry.Message}";
        }

        private Task RotateLogFileIfNeededAsync(string logFilePath)
        {
            try
            {
                var fileInfo = new FileInfo(logFilePath);
                if (fileInfo.Exists && fileInfo.Length > _config.Logging.MaxLogFileSizeMB * 1024 * 1024)
                {
                    var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                    var rotatedPath = Path.Combine(_logDirectory, $"log_{DateTime.Now:yyyyMMdd}_{timestamp}.txt");
                    File.Move(logFilePath, rotatedPath);
                }
            }
            catch
            {
                // 轮转失败，继续使用当前文件
            }
            
            return Task.CompletedTask;
        }

        private Task CleanupOldLogsAsync()
        {
            try
            {
                var logFiles = Directory.GetFiles(_logDirectory, "log_*.txt");
                if (logFiles.Length > _config.Logging.MaxLogFiles)
                {
                    Array.Sort(logFiles);
                    var filesToDelete = logFiles.Take(logFiles.Length - _config.Logging.MaxLogFiles);
                    
                    foreach (var file in filesToDelete)
                    {
                        try
                        {
                            File.Delete(file);
                        }
                        catch
                        {
                            // 删除失败，忽略
                        }
                    }
                }
            }
            catch
            {
                // 清理失败，忽略
            }
            
            return Task.CompletedTask;
        }

        private async Task WriteToFallbackLogAsync(string message)
        {
            try
            {
                var fallbackPath = Path.Combine(Path.GetTempPath(), "NetworkResetter_fallback.log");
                var logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [FALLBACK] {message}\n";
                await File.AppendAllTextAsync(fallbackPath, logEntry);
            }
            catch
            {
                // 连备用日志都写不了，只能放弃
            }
        }

        public async Task<string> GetTodayLogsAsync()
        {
            // 先刷新当前队列中的日志
            await FlushLogsAsync();

            try
            {
                var logFilePath = GetCurrentLogFilePath();
                if (File.Exists(logFilePath))
                {
                    return await File.ReadAllTextAsync(logFilePath);
                }
                return "今日暂无日志记录";
            }
            catch (Exception ex)
            {
                return $"读取日志失败: {ex.Message}";
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (_disposed) return;

            if (disposing)
            {
                _cancellationTokenSource.Cancel();
                
                try
                {
                    // 最后一次刷新日志
                    FlushLogsAsync().Wait(TimeSpan.FromSeconds(5));
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"日志刷新异常: {ex.Message}");
                }
                
                _flushTimer?.Dispose();
                _writeSemaphore?.Dispose();
                _cancellationTokenSource?.Dispose();
            }
            
            _disposed = true;
        }

        ~LoggingService()
        {
            Dispose(false);
        }

        private class LogEntry
        {
            public DateTime Timestamp { get; set; }
            public LogLevel Level { get; set; }
            public string Message { get; set; }
            public int ThreadId { get; set; }
        }

        private enum LogLevel
        {
            Info,
            Warning,
            Error
        }
    }
}