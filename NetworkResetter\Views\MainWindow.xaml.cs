using System;
using System.Windows;
using Microsoft.Extensions.DependencyInjection;
using NetworkResetter.ViewModels;

namespace NetworkResetter.Views
{
    public partial class MainWindow : Window
    {
        private readonly IServiceProvider _serviceProvider;

        public MainWindow(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            InitializeComponent();
            
            // 通过依赖注入获取ViewModel
            DataContext = _serviceProvider.GetRequiredService<MainViewModel>();
        }

        private void Window_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            var viewModel = DataContext as MainViewModel;
            
            // 如果有操作正在进行，询问用户是否确定退出
            if (viewModel?.IsOperationInProgress == true)
            {
                var result = MessageBox.Show(
                    "当前有操作正在进行中，强制退出可能导致系统不稳定。\n\n确定要退出吗？", 
                    "确认退出", 
                    MessageBoxButton.YesNo, 
                    MessageBoxImage.Warning);
                
                if (result == MessageBoxResult.No)
                {
                    e.Cancel = true;
                    return;
                }
            }
            else
            {
                var result = MessageBox.Show("确定要退出网络环境重置器吗？", "确认退出", 
                    MessageBoxButton.YesNo, MessageBoxImage.Question);
                
                if (result == MessageBoxResult.No)
                {
                    e.Cancel = true;
                    return;
                }
            }

            // 清理资源
            try
            {
                if (viewModel is IDisposable disposableViewModel)
                {
                    disposableViewModel.Dispose();
                }
            }
            catch
            {
                // 忽略清理异常
            }
        }
    }
}