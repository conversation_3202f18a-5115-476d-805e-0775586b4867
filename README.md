# 网络环境重置器 (NetworkResetter)

一个专为Windows平台设计的网络环境重置工具，帮助用户快速诊断和修复网络连接问题。

## 功能特性

### 核心功能
- **TCP/IP栈重置**：清理网络协议栈配置，修复底层网络问题
- **网络适配器管理**：重置网络适配器，解决硬件相关问题
- **代理设置重置**：清除系统代理配置，恢复直接连接
- **防火墙重置**：将Windows防火墙恢复到默认设置
- **网络诊断**：全面测试网络连通性、延迟和DNS解析
- **配置备份与恢复**：自动备份网络配置，支持一键恢复

### 用户友好特性
- **中文界面**：完全本地化的中文用户界面
- **操作确认**：重要操作前显示确认对话框
- **详细日志**：记录所有操作过程和结果
- **进度显示**：实时显示操作状态和进度
- **帮助文档**：内置详细的使用说明

## 系统要求

- **操作系统**：Windows 10/11 (x64)
- **运行时**：.NET 7.0 Runtime
- **权限**：管理员权限（UAC提升）

## 安装和使用

### 编译环境要求
1. **Visual Studio 2022** 或 **Visual Studio Code**
2. **.NET 7.0 SDK**
3. **Windows SDK**

### 编译步骤

**方法1：使用自动化脚本（推荐）**
```cmd
# 检查环境
check-environment.cmd

# 验证编译
verify-build.cmd

# 完整构建
build.cmd

# 多目标构建
build-multi-target.cmd
```

**方法2：手动编译**
```cmd
# 1. 设置UTF-8编码（解决中文显示问题）
chcp 65001

# 2. 还原依赖包
dotnet restore NetworkResetter.sln

# 3. 编译项目
dotnet build NetworkResetter.sln -c Release

# 4. 运行程序
dotnet run --project NetworkResetter
```

### 发布选项

**框架依赖发布（需要.NET Runtime）**
```cmd
dotnet publish NetworkResetter -c Release -o publish
```

**独立应用发布（推荐）**
```cmd
# 标准独立发布
dotnet publish NetworkResetter -c Release -r win-x64 --self-contained -o publish

# 单文件发布
dotnet publish NetworkResetter -c Release -r win-x64 --self-contained -p:PublishSingleFile=true -o publish
```

## 使用说明

1. **以管理员身份运行**程序
2. 选择需要的重置功能
3. 程序会自动创建配置备份
4. 确认操作后执行重置
5. 查看操作日志了解详细过程
6. 必要时重启计算机

## 注意事项

⚠️ **重要提醒**
- 本程序需要管理员权限才能正常运行
- 重置操作可能会短暂中断网络连接
- 建议在执行重置前创建系统还原点
- TCP/IP栈重置后建议重启计算机
- 所有操作都会记录在日志文件中

## 技术架构

- **框架**：.NET 7.0 + WPF
- **架构模式**：MVVM
- **依赖项**：
  - System.Management (WMI操作)
  - Microsoft.Win32.Registry (注册表访问)

## 项目结构
```
NetworkResetter/
├── NetworkResetter.sln          # 解决方案文件
└── NetworkResetter/             # 主项目
    ├── Models/                  # 数据模型
    ├── Views/                   # WPF视图
    ├── ViewModels/              # 视图模型
    ├── Services/                # 业务逻辑服务
    ├── Converters/              # 值转换器
    └── app.manifest             # UAC权限配置
```

## 许可证

本项目仅供学习和个人使用。使用本软件造成的任何损失，开发者不承担责任。

## 开发和测试

### 运行测试
```cmd
# 运行所有测试
dotnet test NetworkResetter.Tests

# 运行特定测试类
dotnet test NetworkResetter.Tests --filter "ClassName=SystemCommandServiceTests"

# 生成测试覆盖率报告
dotnet test NetworkResetter.Tests --collect:"XPlat Code Coverage"
```

### 代码质量检查
```cmd
# 代码分析
dotnet build --verbosity normal

# 格式化代码
dotnet format NetworkResetter.sln
```

## 故障排除

### 常见问题

**1. 程序无法启动**
- 确保以管理员身份运行
- 检查是否安装了.NET 7.0 Runtime
- 查看Windows事件日志

**2. 网络重置失败**
- 检查网络适配器是否正常
- 确认没有其他网络管理软件冲突
- 查看应用程序日志文件

**3. 备份恢复失败**
- 检查备份文件是否完整
- 确认有足够的磁盘空间
- 验证文件权限设置

### 日志文件位置
- 应用日志：`%APPDATA%\NetworkResetter\Logs\`
- 配置文件：`%APPDATA%\NetworkResetter\config.json`
- 备份文件：`%APPDATA%\NetworkResetter\Backups\`

### 支持信息
- 操作系统：Windows 10/11 (x64)
- .NET版本：7.0 或更高
- 内存要求：最少 100MB
- 磁盘空间：最少 50MB

## 更新日志

### v1.0.0 (2024-01-XX)
- 初始版本发布
- 支持TCP/IP栈重置
- 支持网络适配器重置
- 支持代理设置重置
- 支持防火墙重置
- 支持网络诊断
- 支持配置备份和恢复
- 完整的日志记录系统
- 现代化的用户界面

## 贡献指南

欢迎提交问题报告和功能请求。如果您想贡献代码：

1. Fork 本项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 免责声明

本软件按"原样"提供，不提供任何明示或暗示的保证。使用本软件的风险由用户自行承担。作者不对使用本软件造成的任何损失承担责任。

在使用网络重置功能之前，建议：
1. 创建系统还原点
2. 备份重要的网络配置
3. 在测试环境中先行验证