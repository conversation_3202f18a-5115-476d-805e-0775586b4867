# NetworkResetter 管理员启动脚本

Write-Host "NetworkResetter - 网络环境重置器" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green
Write-Host ""

# 检查是否已经是管理员权限
$currentPrincipal = New-Object Security.Principal.WindowsPrincipal([Security.Principal.WindowsIdentity]::GetCurrent())
$isAdmin = $currentPrincipal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)

if ($isAdmin) {
    Write-Host "✓ 已具有管理员权限" -ForegroundColor Green
    Write-Host "正在启动应用程序..." -ForegroundColor Yellow
    
    $appPath = Join-Path $PSScriptRoot "NetworkResetter\bin\Release\net8.0-windows\win-x64\NetworkResetter.exe"
    
    if (Test-Path $appPath) {
        Start-Process -FilePath $appPath
        Write-Host "✓ 应用程序已启动" -ForegroundColor Green
    } else {
        Write-Host "✗ 应用程序文件不存在，请先编译项目" -ForegroundColor Red
        Write-Host "运行: dotnet build --configuration Release" -ForegroundColor Yellow
    }
} else {
    Write-Host "⚠ 需要管理员权限" -ForegroundColor Yellow
    Write-Host "正在请求权限提升..." -ForegroundColor Yellow
    
    try {
        $appPath = Join-Path $PSScriptRoot "NetworkResetter\bin\Release\net8.0-windows\win-x64\NetworkResetter.exe"
        
        if (Test-Path $appPath) {
            Start-Process -FilePath $appPath -Verb RunAs
            Write-Host "✓ 权限提升请求已发送" -ForegroundColor Green
        } else {
            Write-Host "✗ 应用程序文件不存在，请先编译项目" -ForegroundColor Red
            Write-Host "运行: dotnet build --configuration Release" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "✗ 启动失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "按任意键退出..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")