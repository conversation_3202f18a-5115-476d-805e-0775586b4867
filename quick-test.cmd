@echo off
chcp 65001 >nul
echo NetworkResetter - Quick Test
echo ==============================

echo Current directory: %CD%

echo.
echo Checking if solution file exists...
if exist "NetworkResetter.sln" (
    echo SUCCESS: NetworkResetter.sln found
) else (
    echo ERROR: NetworkResetter.sln not found
    dir *.sln
    pause
    exit /b 1
)

echo.
echo Checking .NET SDK...
dotnet --version
if %errorlevel% neq 0 (
    echo ERROR: .NET SDK not found
    pause
    exit /b 1
)

echo.
echo Testing restore...
dotnet restore NetworkResetter.sln --verbosity minimal
if %errorlevel% neq 0 (
    echo ERROR: Package restore failed
    pause
    exit /b 1
)

echo.
echo Testing build...
dotnet build NetworkResetter.sln -c Debug --verbosity minimal
if %errorlevel% neq 0 (
    echo ERROR: Build failed
    pause
    exit /b 1
)

echo.
echo ==============================
echo Quick test completed successfully!
echo ==============================
pause