<Window x:Class="NetworkResetter.Views.PerformanceMonitorWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="性能监控" Height="500" Width="700"
        WindowStartupLocation="CenterOwner">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题和刷新按钮 -->
        <Grid Grid.Row="0" Margin="0,0,0,15">
            <TextBlock Text="系统性能监控" FontSize="18" FontWeight="Bold" 
                       VerticalAlignment="Center"/>
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <CheckBox x:Name="AutoRefreshCheckBox" Content="自动刷新" 
                          IsChecked="True" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <Button Content="立即刷新" Click="RefreshButton_Click" 
                        Width="80" Height="30"/>
            </StackPanel>
        </Grid>

        <!-- 性能指标 -->
        <ScrollViewer Grid.Row="1">
            <StackPanel>
                <!-- CPU使用率 -->
                <GroupBox Header="CPU使用率" Margin="0,0,0,15">
                    <Grid Margin="10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="100"/>
                        </Grid.ColumnDefinitions>
                        <ProgressBar x:Name="CpuProgressBar" Height="20" 
                                     Minimum="0" Maximum="100"/>
                        <TextBlock x:Name="CpuPercentText" Grid.Column="1" 
                                   HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Grid>
                </GroupBox>

                <!-- 内存使用 -->
                <GroupBox Header="内存使用" Margin="0,0,0,15">
                    <Grid Margin="10">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="100"/>
                        </Grid.ColumnDefinitions>
                        
                        <ProgressBar x:Name="MemoryProgressBar" Grid.Row="0" Height="20" 
                                     Minimum="0" Maximum="100"/>
                        <TextBlock x:Name="MemoryPercentText" Grid.Row="0" Grid.Column="1" 
                                   HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        <TextBlock x:Name="MemoryDetailsText" Grid.Row="1" Grid.ColumnSpan="2" 
                                   Margin="0,5,0,0" FontSize="12" Foreground="Gray"/>
                    </Grid>
                </GroupBox>

                <!-- 网络活动 -->
                <GroupBox Header="网络活动" Margin="0,0,0,15">
                    <Grid Margin="10">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock x:Name="NetworkSentText" Grid.Row="0" Margin="0,2"/>
                        <TextBlock x:Name="NetworkReceivedText" Grid.Row="1" Margin="0,2"/>
                        <TextBlock x:Name="NetworkConnectionsText" Grid.Row="2" Margin="0,2"/>
                    </Grid>
                </GroupBox>

                <!-- 应用程序信息 -->
                <GroupBox Header="应用程序信息" Margin="0,0,0,15">
                    <Grid Margin="10">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock x:Name="AppMemoryText" Grid.Row="0" Margin="0,2"/>
                        <TextBlock x:Name="AppCpuText" Grid.Row="1" Margin="0,2"/>
                        <TextBlock x:Name="AppUptimeText" Grid.Row="2" Margin="0,2"/>
                        <TextBlock x:Name="AppThreadsText" Grid.Row="3" Margin="0,2"/>
                    </Grid>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>

        <!-- 关闭按钮 -->
        <Button Grid.Row="2" Content="关闭" Click="CloseButton_Click" 
                Width="80" Height="30" HorizontalAlignment="Right" 
                Margin="0,15,0,0"/>
    </Grid>
</Window>