using System;
using System.Threading.Tasks;
using System.Windows;

namespace NetworkResetter.Common.UI
{
    public static class NotificationService
    {
        public static async Task<bool> ShowConfirmationAsync(string title, string message)
        {
            return await Task.Run(() =>
            {
                var result = MessageBox.Show(
                    message, 
                    title, 
                    MessageBoxButton.YesNo, 
                    MessageBoxImage.Question,
                    MessageBoxResult.No);
                
                return result == MessageBoxResult.Yes;
            });
        }

        public static async Task ShowSuccessAsync(string title, string message)
        {
            await Task.Run(() =>
            {
                MessageBox.Show(
                    message, 
                    title, 
                    MessageBoxButton.OK, 
                    MessageBoxImage.Information);
            });
        }

        public static async Task ShowWarningAsync(string title, string message)
        {
            await Task.Run(() =>
            {
                MessageBox.Show(
                    message, 
                    title, 
                    MessageBoxButton.OK, 
                    MessageBoxImage.Warning);
            });
        }

        public static async Task ShowErrorAsync(string title, string message, Exception exception = null)
        {
            await Task.Run(() =>
            {
                var fullMessage = exception != null ? 
                    $"{message}\n\n详细错误信息:\n{exception.Message}" : 
                    message;

                MessageBox.Show(
                    fullMessage, 
                    title, 
                    MessageBoxButton.OK, 
                    MessageBoxImage.Error);
            });
        }

        public static async Task ShowOperationResultAsync(Models.OperationResult result, string operationName)
        {
            await Task.Run(() =>
            {
                var dialog = new Views.OperationResultDialog(result, operationName);
                dialog.ShowDialog();
            });
        }
    }
}