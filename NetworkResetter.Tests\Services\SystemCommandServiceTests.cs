using FluentAssertions;
using Moq;
using NetworkResetter.Common.Interfaces;
using NetworkResetter.Services;
using Xunit;

namespace NetworkResetter.Tests.Services
{
    public class SystemCommandServiceTests
    {
        private readonly Mock<ILoggingService> _mockLogger;
        private readonly SystemCommandService _service;

        public SystemCommandServiceTests()
        {
            _mockLogger = new Mock<ILoggingService>();
            _service = new SystemCommandService(_mockLogger.Object);
        }

        [Fact]
        public async Task ExecuteCommandAsync_ValidCommand_ReturnsSuccess()
        {
            // Arrange
            var command = "echo test";

            // Act
            var result = await _service.ExecuteCommandAsync(command);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            result.Output.Should().Contain("test");
            result.ExitCode.Should().Be(0);
        }

        [Fact]
        public async Task ExecuteCommandAsync_InvalidCommand_ReturnsFailure()
        {
            // Arrange
            var command = "nonexistentcommand";

            // Act
            var result = await _service.ExecuteCommandAsync(command);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeFalse();
            result.ExitCode.Should().NotBe(0);
        }

        [Theory]
        [InlineData("echo test", new[] { "echo" }, true)]  // 使用简单的 echo 命令
        [InlineData("dir", new[] { "dir" }, true)]         // 使用 dir 命令
        [InlineData("del /f /q *.*", new[] { "echo", "dir" }, false)]  // 危险命令应该被阻止
        [InlineData("format c:", new[] { "echo", "dir" }, false)]      // 危险命令应该被阻止
        public async Task ExecuteCommandSafeAsync_CommandValidation_WorksCorrectly(
            string command, string[] allowedCommands, bool shouldSucceed)
        {
            // Act
            var result = await _service.ExecuteCommandSafeAsync(command, allowedCommands);

            // Assert
            if (shouldSucceed)
            {
                // 对于应该成功的命令，如果失败了，检查是否是验证问题
                if (!result.Success && result.Error.Contains("不在允许列表中"))
                {
                    result.Success.Should().BeTrue("命令应该通过验证");
                }
                // 如果是其他原因失败（如权限），那是可以接受的
            }
            else
            {
                result.Success.Should().BeFalse();
                result.Error.Should().Contain("不在允许列表中");
            }
        }

        [Fact]
        public async Task ExecuteCommandAsync_LongRunningCommand_RespectsTimeout()
        {
            // Arrange
            var command = "ping -n 10 127.0.0.1"; // 长时间运行的命令
            var timeout = 2000; // 2秒超时

            // Act
            var result = await _service.ExecuteCommandAsync(command, timeout);

            // Assert
            result.Should().NotBeNull();
            result.ExecutionTime.Should().BeLessThan(TimeSpan.FromSeconds(3)); // 应该在超时时间内返回
            result.Success.Should().BeFalse(); // 超时应该返回失败
            result.Error.Should().Contain("超时");
        }
    }
}