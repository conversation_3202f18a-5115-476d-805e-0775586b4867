using Microsoft.Extensions.DependencyInjection;
using NetworkResetter.Common.Configuration;
using NetworkResetter.Common.Interfaces;
using NetworkResetter.Common.Services;
using NetworkResetter.Services;
using NetworkResetter.ViewModels;

namespace NetworkResetter.Common.DependencyInjection
{
    public static class ServiceCollectionExtensions
    {
        public static IServiceCollection AddNetworkResetterServices(this IServiceCollection services)
        {
            // 注册配置
            services.AddSingleton(AppConfig.Instance);
            
            // 注册核心服务 - 使用 Scoped 避免状态问题
            services.AddScoped<ILoggingService, LoggingService>();
            services.AddScoped<ISystemCommandService, SystemCommandService>();
            services.AddScoped<INetworkService, NetworkService>();
            services.AddScoped<IProxyService, ProxyService>();
            services.AddScoped<IFirewallService, FirewallService>();
            services.AddScoped<IBackupService, BackupService>();
            services.AddScoped<HealthCheckService>();
            
            // 注册UI服务
            services.AddScoped<NotificationService>();

            // 注册ViewModels
            services.AddTransient<MainViewModel>();

            return services;
        }
    }
}