using System;
using System.Linq;
using System.Windows;
using NetworkResetter.Common.Configuration;
using NetworkResetter.Common.Interfaces;

namespace NetworkResetter.Views
{
    public partial class SettingsWindow : Window
    {
        private readonly ILoggingService _logger;
        private readonly AppConfig _config;

        public SettingsWindow(ILoggingService logger)
        {
            InitializeComponent();
            _logger = logger;
            _config = AppConfig.Instance;
            
            LoadSettings();
        }

        private void LoadSettings()
        {
            try
            {
                // 网络诊断设置
                PingHostsTextBox.Text = string.Join("\n", _config.NetworkDiagnostic.PingHosts);
                PingTimeoutTextBox.Text = _config.NetworkDiagnostic.PingTimeoutMs.ToString();
                DnsTestSitesTextBox.Text = string.Join("\n", _config.NetworkDiagnostic.DnsTestSites);
                DnsTimeoutTextBox.Text = _config.NetworkDiagnostic.DnsTimeoutMs.ToString();

                // 日志设置
                switch (_config.Logging.LogLevel.ToLower())
                {
                    case "info":
                        LogLevelInfoRadio.IsChecked = true;
                        break;
                    case "warning":
                        LogLevelWarningRadio.IsChecked = true;
                        break;
                    case "error":
                        LogLevelErrorRadio.IsChecked = true;
                        break;
                }

                MaxLogFileSizeTextBox.Text = _config.Logging.MaxLogFileSizeMB.ToString();
                MaxLogFilesTextBox.Text = _config.Logging.MaxLogFiles.ToString();
                EnableConsoleLoggingCheckBox.IsChecked = _config.Logging.EnableConsoleLogging;

                // 安全设置
                EnableCommandValidationCheckBox.IsChecked = _config.Security.EnableCommandValidation;
                CommandTimeoutTextBox.Text = _config.Security.CommandTimeoutMs.ToString();
                AllowedCommandsTextBox.Text = string.Join("\n", _config.Security.AllowedCommands);

                // 界面设置
                LightThemeRadio.IsChecked = _config.UI.Theme == "Light";
                DarkThemeRadio.IsChecked = _config.UI.Theme == "Dark";
                ShowAdvancedOptionsCheckBox.IsChecked = _config.UI.ShowAdvancedOptions;
                OperationTimeoutTextBox.Text = _config.UI.OperationTimeoutMs.ToString();
            }
            catch (Exception ex)
            {
                _logger?.LogErrorAsync("加载设置失败", ex);
                MessageBox.Show($"加载设置失败: {ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void Apply_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                await ApplySettingsAsync();
                MessageBox.Show("设置已应用", "成功", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                await _logger.LogErrorAsync("应用设置失败", ex);
                MessageBox.Show($"应用设置失败: {ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void OK_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                await ApplySettingsAsync();
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                await _logger.LogErrorAsync("保存设置失败", ex);
                MessageBox.Show($"保存设置失败: {ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void RestoreDefaults_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("确定要恢复所有设置到默认值吗？", "确认", 
                MessageBoxButton.YesNo, MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                RestoreDefaultSettings();
            }
        }

        private void RestoreDefaultSettings()
        {
            // 恢复默认的网络诊断设置
            PingHostsTextBox.Text = "*********\n114.114.114.114\n8.8.8.8";
            PingTimeoutTextBox.Text = "5000";
            DnsTestSitesTextBox.Text = "www.baidu.com\nwww.qq.com\nwww.microsoft.com";
            DnsTimeoutTextBox.Text = "10000";

            // 恢复默认的日志设置
            LogLevelInfoRadio.IsChecked = true;
            MaxLogFileSizeTextBox.Text = "10";
            MaxLogFilesTextBox.Text = "5";
            EnableConsoleLoggingCheckBox.IsChecked = false;

            // 恢复默认的安全设置
            EnableCommandValidationCheckBox.IsChecked = true;
            CommandTimeoutTextBox.Text = "30000";
            AllowedCommandsTextBox.Text = "netsh\nipconfig\nnetstat\nping";

            // 恢复默认的界面设置
            LightThemeRadio.IsChecked = true;
            ShowAdvancedOptionsCheckBox.IsChecked = false;
            OperationTimeoutTextBox.Text = "60000";
        }

        private async System.Threading.Tasks.Task ApplySettingsAsync()
        {
            // 应用网络诊断设置
            _config.NetworkDiagnostic.PingHosts = PingHostsTextBox.Text
                .Split('\n')
                .Select(h => h.Trim())
                .Where(h => !string.IsNullOrEmpty(h))
                .ToList();

            if (int.TryParse(PingTimeoutTextBox.Text, out var pingTimeout))
                _config.NetworkDiagnostic.PingTimeoutMs = pingTimeout;

            _config.NetworkDiagnostic.DnsTestSites = DnsTestSitesTextBox.Text
                .Split('\n')
                .Select(s => s.Trim())
                .Where(s => !string.IsNullOrEmpty(s))
                .ToList();

            if (int.TryParse(DnsTimeoutTextBox.Text, out var dnsTimeout))
                _config.NetworkDiagnostic.DnsTimeoutMs = dnsTimeout;

            // 应用日志设置
            if (LogLevelInfoRadio.IsChecked == true)
                _config.Logging.LogLevel = "Info";
            else if (LogLevelWarningRadio.IsChecked == true)
                _config.Logging.LogLevel = "Warning";
            else if (LogLevelErrorRadio.IsChecked == true)
                _config.Logging.LogLevel = "Error";

            if (int.TryParse(MaxLogFileSizeTextBox.Text, out var maxLogSize))
                _config.Logging.MaxLogFileSizeMB = maxLogSize;

            if (int.TryParse(MaxLogFilesTextBox.Text, out var maxLogFiles))
                _config.Logging.MaxLogFiles = maxLogFiles;

            _config.Logging.EnableConsoleLogging = EnableConsoleLoggingCheckBox.IsChecked == true;

            // 应用安全设置
            _config.Security.EnableCommandValidation = EnableCommandValidationCheckBox.IsChecked == true;

            if (int.TryParse(CommandTimeoutTextBox.Text, out var commandTimeout))
                _config.Security.CommandTimeoutMs = commandTimeout;

            _config.Security.AllowedCommands = AllowedCommandsTextBox.Text
                .Split('\n')
                .Select(c => c.Trim())
                .Where(c => !string.IsNullOrEmpty(c))
                .ToList();

            // 应用界面设置
            _config.UI.Theme = LightThemeRadio.IsChecked == true ? "Light" : "Dark";
            _config.UI.ShowAdvancedOptions = ShowAdvancedOptionsCheckBox.IsChecked == true;

            if (int.TryParse(OperationTimeoutTextBox.Text, out var operationTimeout))
                _config.UI.OperationTimeoutMs = operationTimeout;

            // 保存配置
            _config.SaveConfig();
            
            await _logger.LogInfoAsync("应用程序设置已更新");
        }
    }
}