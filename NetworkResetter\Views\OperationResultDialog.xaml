<Window x:Class="NetworkResetter.Views.OperationResultDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="操作结果" Height="400" Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 操作名称 -->
        <TextBlock Grid.Row="0" x:Name="OperationNameText" 
                   FontSize="18" FontWeight="Bold" 
                   Margin="0,0,0,10"/>

        <!-- 结果状态 -->
        <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,15">
            <Ellipse x:Name="StatusIndicator" Width="16" Height="16" 
                     VerticalAlignment="Center" Margin="0,0,10,0"/>
            <TextBlock x:Name="StatusText" FontSize="14" FontWeight="Medium" 
                       VerticalAlignment="Center"/>
        </StackPanel>

        <!-- 详细信息 -->
        <GroupBox Grid.Row="2" Header="详细信息" Margin="0,0,0,15">
            <ScrollViewer>
                <TextBlock x:Name="DetailsText" 
                           TextWrapping="Wrap" 
                           FontFamily="Consolas" 
                           FontSize="12"
                           Padding="10"/>
            </ScrollViewer>
        </GroupBox>

        <!-- 按钮 -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" 
                    HorizontalAlignment="Right">
            <Button Content="复制详情" Click="CopyDetails_Click" 
                    Width="80" Margin="0,0,10,0"/>
            <Button Content="确定" Click="OK_Click" 
                    Width="80" IsDefault="True"/>
        </StackPanel>
    </Grid>
</Window>