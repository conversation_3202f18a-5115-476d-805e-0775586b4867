using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading;
using NetworkResetter.Common.Configuration;
using NetworkResetter.Common.Interfaces;

namespace NetworkResetter.Common.Services
{
    public interface ILanguageService
    {
        /// <summary>
        /// 获取当前语言设置
        /// </summary>
        string CurrentLanguage { get; }

        /// <summary>
        /// 获取支持的语言列表
        /// </summary>
        Dictionary<string, string> SupportedLanguages { get; }

        /// <summary>
        /// 设置应用程序语言
        /// </summary>
        /// <param name="languageCode">语言代码 (如: zh-CN, en-US)</param>
        void SetLanguage(string languageCode);

        /// <summary>
        /// 保存语言设置到配置文件
        /// </summary>
        void SaveLanguageSetting();

        /// <summary>
        /// 获取本地化文本
        /// </summary>
        /// <param name="key">文本键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>本地化文本</returns>
        string GetText(string key, string defaultValue = null);
    }

    public class LanguageService : ILanguageService
    {
        private readonly AppConfig _config;
        private readonly ILoggingService _logger;
        private string _currentLanguage;

        // 支持的语言列表
        private readonly Dictionary<string, string> _supportedLanguages = new()
        {
            { "zh-CN", "简体中文" },
            { "zh-TW", "繁體中文" },
            { "en-US", "English" },
            { "ja-JP", "日本語" },
            { "ko-KR", "한국어" }
        };

        // 本地化文本字典
        private readonly Dictionary<string, Dictionary<string, string>> _localizedTexts = new()
        {
            ["zh-CN"] = new Dictionary<string, string>
            {
                ["app_title"] = "网络重置工具",
                ["reset_tcpip"] = "重置 TCP/IP 栈",
                ["reset_adapters"] = "重置网络适配器",
                ["reset_proxy"] = "重置代理设置",
                ["reset_firewall"] = "重置防火墙",
                ["create_backup"] = "创建备份",
                ["restore_backup"] = "恢复备份",
                ["network_diagnostics"] = "网络诊断",
                ["settings"] = "设置",
                ["help"] = "帮助",
                ["exit"] = "退出",
                ["operation_success"] = "操作成功",
                ["operation_failed"] = "操作失败",
                ["language"] = "语言",
                ["theme"] = "主题",
                ["advanced_options"] = "高级选项",
                ["timeout_settings"] = "超时设置",
                ["logging_settings"] = "日志设置",
                ["security_settings"] = "安全设置"
            },
            ["en-US"] = new Dictionary<string, string>
            {
                ["app_title"] = "Network Resetter",
                ["reset_tcpip"] = "Reset TCP/IP Stack",
                ["reset_adapters"] = "Reset Network Adapters",
                ["reset_proxy"] = "Reset Proxy Settings",
                ["reset_firewall"] = "Reset Firewall",
                ["create_backup"] = "Create Backup",
                ["restore_backup"] = "Restore Backup",
                ["network_diagnostics"] = "Network Diagnostics",
                ["settings"] = "Settings",
                ["help"] = "Help",
                ["exit"] = "Exit",
                ["operation_success"] = "Operation Successful",
                ["operation_failed"] = "Operation Failed",
                ["language"] = "Language",
                ["theme"] = "Theme",
                ["advanced_options"] = "Advanced Options",
                ["timeout_settings"] = "Timeout Settings",
                ["logging_settings"] = "Logging Settings",
                ["security_settings"] = "Security Settings"
            }
        };

        public LanguageService(ILoggingService logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _config = AppConfig.Instance;
            _currentLanguage = _config.UI.Language;
            
            // 初始化时设置系统语言
            InitializeLanguage();
        }

        public string CurrentLanguage => _currentLanguage;

        public Dictionary<string, string> SupportedLanguages => new(_supportedLanguages);

        public void SetLanguage(string languageCode)
        {
            if (string.IsNullOrWhiteSpace(languageCode))
            {
                throw new ArgumentException("语言代码不能为空", nameof(languageCode));
            }

            if (!_supportedLanguages.ContainsKey(languageCode))
            {
                throw new ArgumentException($"不支持的语言代码: {languageCode}", nameof(languageCode));
            }

            try
            {
                _currentLanguage = languageCode;
                _config.UI.Language = languageCode;

                // 设置线程的UI文化
                var culture = new CultureInfo(languageCode);
                Thread.CurrentThread.CurrentCulture = culture;
                Thread.CurrentThread.CurrentUICulture = culture;

                // 设置应用程序域的默认文化
                CultureInfo.DefaultThreadCurrentCulture = culture;
                CultureInfo.DefaultThreadCurrentUICulture = culture;

                _logger?.LogInfoAsync($"语言已切换到: {_supportedLanguages[languageCode]} ({languageCode})");
            }
            catch (Exception ex)
            {
                _logger?.LogErrorAsync($"设置语言失败: {languageCode}", ex);
                throw new InvalidOperationException($"设置语言失败: {ex.Message}", ex);
            }
        }

        public void SaveLanguageSetting()
        {
            try
            {
                _config.SaveConfig();
                _logger?.LogInfoAsync($"语言设置已保存: {_currentLanguage}");
            }
            catch (Exception ex)
            {
                _logger?.LogErrorAsync("保存语言设置失败", ex);
                throw new InvalidOperationException($"保存语言设置失败: {ex.Message}", ex);
            }
        }

        public string GetText(string key, string defaultValue = null)
        {
            if (string.IsNullOrWhiteSpace(key))
            {
                return defaultValue ?? key;
            }

            try
            {
                // 尝试获取当前语言的文本
                if (_localizedTexts.TryGetValue(_currentLanguage, out var currentLanguageTexts) &&
                    currentLanguageTexts.TryGetValue(key, out var text))
                {
                    return text;
                }

                // 如果当前语言没有，尝试使用中文作为后备
                if (_currentLanguage != "zh-CN" &&
                    _localizedTexts.TryGetValue("zh-CN", out var chineseTexts) &&
                    chineseTexts.TryGetValue(key, out var chineseText))
                {
                    return chineseText;
                }

                // 如果都没有，尝试使用英文作为后备
                if (_currentLanguage != "en-US" &&
                    _localizedTexts.TryGetValue("en-US", out var englishTexts) &&
                    englishTexts.TryGetValue(key, out var englishText))
                {
                    return englishText;
                }

                // 最后返回默认值或键名
                return defaultValue ?? key;
            }
            catch (Exception ex)
            {
                _logger?.LogErrorAsync($"获取本地化文本失败: {key}", ex);
                return defaultValue ?? key;
            }
        }

        private void InitializeLanguage()
        {
            try
            {
                // 如果配置中的语言不支持，使用系统默认语言
                if (!_supportedLanguages.ContainsKey(_currentLanguage))
                {
                    var systemLanguage = CultureInfo.CurrentUICulture.Name;
                    
                    // 检查系统语言是否支持
                    if (_supportedLanguages.ContainsKey(systemLanguage))
                    {
                        _currentLanguage = systemLanguage;
                    }
                    else
                    {
                        // 尝试匹配语言族 (如 zh-TW -> zh-CN)
                        var languageFamily = systemLanguage.Split('-')[0];
                        var matchedLanguage = _supportedLanguages.Keys
                            .FirstOrDefault(lang => lang.StartsWith(languageFamily));
                        
                        _currentLanguage = matchedLanguage ?? "zh-CN"; // 默认使用中文
                    }
                    
                    _config.UI.Language = _currentLanguage;
                }

                // 设置应用程序语言
                SetLanguage(_currentLanguage);
            }
            catch (Exception ex)
            {
                _logger?.LogErrorAsync("初始化语言设置失败", ex);
                // 发生错误时使用默认中文
                _currentLanguage = "zh-CN";
                _config.UI.Language = _currentLanguage;
            }
        }
    }
}