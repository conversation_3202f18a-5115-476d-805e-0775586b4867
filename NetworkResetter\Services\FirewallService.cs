using System;
using System.Threading.Tasks;
using NetworkResetter.Common.Interfaces;
using NetworkResetter.Models;

namespace NetworkResetter.Services
{
    public class FirewallService : IFirewallService
    {
        private readonly ILoggingService _logger;
        private readonly ISystemCommandService _commandService;

        public FirewallService(ILoggingService logger, ISystemCommandService commandService)
        {
            _logger = logger;
            _commandService = commandService;
        }

        public async Task<OperationResult> ResetFirewallToDefaultAsync()
        {
            try
            {
                await _logger.LogInfoAsync("开始重置防火墙设置");

                var commands = new[]
                {
                    "netsh advfirewall reset",
                    "netsh advfirewall set allprofiles state on"
                };

                var result = OperationResult.CreateSuccess("防火墙重置完成");
                var failedCommands = 0;

                foreach (var command in commands)
                {
                    var commandResult = await _commandService.ExecuteCommandSafeAsync(
                        command, new[] { "netsh" });
                    
                    if (commandResult.Success)
                    {
                        result.AddDetail($"✓ {command}");
                        await _logger.LogInfoAsync($"命令执行成功: {command}");
                    }
                    else
                    {
                        failedCommands++;
                        result.AddDetail($"✗ {command}: {commandResult.Error}");
                        await _logger.LogWarningAsync($"命令执行失败: {command} - {commandResult.Error}");
                    }
                }

                if (failedCommands > 0)
                {
                    result.Success = failedCommands < commands.Length;
                    result.Message = failedCommands == commands.Length ? 
                        "防火墙重置失败" : "防火墙重置部分完成";
                }

                await _logger.LogInfoAsync($"防火墙重置操作完成，成功: {result.Success}");
                return result;
            }
            catch (Exception ex)
            {
                await _logger.LogErrorAsync("重置防火墙异常", ex);
                return OperationResult.CreateFailure("防火墙重置异常", "FIREWALL_RESET_EXCEPTION", ex);
            }
        }

        public async Task<OperationResult> EnableFirewallAsync()
        {
            try
            {
                await _logger.LogInfoAsync("启用防火墙");
                
                var commandResult = await _commandService.ExecuteCommandSafeAsync(
                    "netsh advfirewall set allprofiles state on", new[] { "netsh" });
                
                if (commandResult.Success)
                {
                    await _logger.LogInfoAsync("防火墙启用成功");
                    return OperationResult.CreateSuccess("防火墙已启用")
                        .AddDetail("所有配置文件的防火墙已启用");
                }
                else
                {
                    await _logger.LogErrorAsync($"启用防火墙失败: {commandResult.Error}");
                    return OperationResult.CreateFailure("启用防火墙失败", "FIREWALL_ENABLE_FAILED")
                        .AddDetail(commandResult.Error);
                }
            }
            catch (Exception ex)
            {
                await _logger.LogErrorAsync("启用防火墙异常", ex);
                return OperationResult.CreateFailure("启用防火墙异常", "FIREWALL_ENABLE_EXCEPTION", ex);
            }
        }

        public async Task<OperationResult> DisableFirewallAsync()
        {
            try
            {
                await _logger.LogInfoAsync("禁用防火墙");
                
                var commandResult = await _commandService.ExecuteCommandSafeAsync(
                    "netsh advfirewall set allprofiles state off", new[] { "netsh" });
                
                if (commandResult.Success)
                {
                    await _logger.LogInfoAsync("防火墙禁用成功");
                    return OperationResult.CreateSuccess("防火墙已禁用")
                        .AddDetail("⚠️ 警告：防火墙已禁用，系统安全性降低");
                }
                else
                {
                    await _logger.LogErrorAsync($"禁用防火墙失败: {commandResult.Error}");
                    return OperationResult.CreateFailure("禁用防火墙失败", "FIREWALL_DISABLE_FAILED")
                        .AddDetail(commandResult.Error);
                }
            }
            catch (Exception ex)
            {
                await _logger.LogErrorAsync("禁用防火墙异常", ex);
                return OperationResult.CreateFailure("禁用防火墙异常", "FIREWALL_DISABLE_EXCEPTION", ex);
            }
        }


    }
}