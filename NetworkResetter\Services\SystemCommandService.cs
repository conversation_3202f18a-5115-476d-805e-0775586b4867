using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using NetworkResetter.Common.Configuration;
using NetworkResetter.Common.Interfaces;
using NetworkResetter.Models;

namespace NetworkResetter.Services
{
    public class SystemCommandService : ISystemCommandService
    {
        private readonly ILoggingService _logger;
        private readonly AppConfig _config;
        
        // 允许的命令白名单
        private static readonly HashSet<string> ALLOWED_COMMANDS = new(StringComparer.OrdinalIgnoreCase)
        {
            "netsh", "ipconfig", "ping", "nslookup", "tracert", "arp", "route", "netstat"
        };
        
        // 危险参数黑名单
        private static readonly string[] DANGEROUS_PATTERNS = 
        {
            @"[;&|`$()]",           // 命令分隔符和特殊字符
            @">\s*\w+",             // 重定向
            @"<\s*\w+",             // 输入重定向
            @"\|\s*\w+",            // 管道
            @"^\s*del\s+",          // 删除命令
            @"^\s*rd\s+",           // 删除目录
            @"^\s*format\s+",       // 格式化
            @"^\s*shutdown\s+",     // 关机
            @"^\s*reboot\s+",       // 重启
            @"^\s*reg\s+",          // 注册表操作
            @"^\s*sc\s+",           // 服务控制
            @"^\s*wmic\s+",         // WMI命令
            @"\.exe\s*$",           // 执行文件
            @"\.bat\s*$",           // 批处理文件
            @"\.cmd\s*$",           // 命令文件
            @"\.ps1\s*$"            // PowerShell脚本
        };

        public SystemCommandService(ILoggingService logger)
        {
            _logger = logger;
            _config = AppConfig.Instance;
        }

        public async Task<CommandResult> ExecuteCommandAsync(string command, int timeoutMs = 30000)
        {
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                // 安全验证
                var validationResult = ValidateCommand(command);
                if (!validationResult.IsValid)
                {
                    await _logger.LogWarningAsync($"命令验证失败: {command} - {validationResult.ErrorMessage}");
                    return new CommandResult
                    {
                        Success = false,
                        Error = $"命令验证失败: {validationResult.ErrorMessage}",
                        ExitCode = -1,
                        ExecutionTime = stopwatch.Elapsed
                    };
                }

                await _logger.LogInfoAsync($"执行命令: {SanitizeCommandForLog(command)}");
                await LogSecurityEventAsync("COMMAND_EXECUTION", $"执行命令: {SanitizeCommandForLog(command)}");

                using var process = new Process();
                process.StartInfo = new ProcessStartInfo
                {
                    FileName = "cmd.exe",
                    Arguments = $"/c chcp 65001 >nul && {EscapeCommand(command)}",
                    CreateNoWindow = true,
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    StandardOutputEncoding = System.Text.Encoding.UTF8,
                    StandardErrorEncoding = System.Text.Encoding.UTF8
                };

                var outputBuilder = new System.Text.StringBuilder();
                var errorBuilder = new System.Text.StringBuilder();

                process.OutputDataReceived += (sender, e) =>
                {
                    if (!string.IsNullOrEmpty(e.Data))
                        outputBuilder.AppendLine(e.Data);
                };

                process.ErrorDataReceived += (sender, e) =>
                {
                    if (!string.IsNullOrEmpty(e.Data))
                        errorBuilder.AppendLine(e.Data);
                };

                process.Start();
                process.BeginOutputReadLine();
                process.BeginErrorReadLine();

                using var cts = new CancellationTokenSource(timeoutMs);
                
                try
                {
                    // 使用 WaitForExitAsync 支持取消令牌
                    await process.WaitForExitAsync(cts.Token);
                }
                catch (OperationCanceledException)
                {
                    try
                    {
                        process.Kill();
                        process.WaitForExit(1000); // 等待进程真正结束
                    }
                    catch { }
                    
                    stopwatch.Stop();
                    await _logger.LogWarningAsync($"命令执行超时: {command}");
                    
                    return new CommandResult
                    {
                        Success = false,
                        Error = "命令执行超时",
                        ExitCode = -1,
                        ExecutionTime = stopwatch.Elapsed
                    };
                }

                stopwatch.Stop();
                
                var result = new CommandResult
                {
                    Success = process.ExitCode == 0,
                    Output = outputBuilder.ToString(),
                    Error = errorBuilder.ToString(),
                    ExitCode = process.ExitCode,
                    ExecutionTime = stopwatch.Elapsed
                };

                await _logger.LogInfoAsync($"命令执行完成，退出码: {process.ExitCode}, 耗时: {stopwatch.ElapsedMilliseconds}ms");
                
                if (!result.Success)
                {
                    await _logger.LogWarningAsync($"命令执行失败: {result.Error}");
                }

                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                await _logger.LogErrorAsync($"命令执行异常: {command}", ex);
                
                return new CommandResult
                {
                    Success = false,
                    Error = ex.Message,
                    ExitCode = -1,
                    ExecutionTime = stopwatch.Elapsed
                };
            }
        }

        public async Task<CommandResult> ExecuteCommandSafeAsync(string command, string[] allowedCommands, int timeoutMs = 30000)
        {
            if (_config.Security.EnableCommandValidation)
            {
                if (!IsCommandSafe(command, allowedCommands))
                {
                    await _logger.LogWarningAsync($"危险命令被阻止: {SanitizeCommandForLog(command)}");
                    return new CommandResult
                    {
                        Success = false,
                        Error = "命令不在允许列表中",
                        ExitCode = -1
                    };
                }
            }

            return await ExecuteCommandAsync(command, timeoutMs);
        }

        private bool IsCommandSafe(string command, string[] allowedCommands)
        {
            if (string.IsNullOrWhiteSpace(command))
                return false;

            // 移除多余空格并转换为小写
            var normalizedCommand = Regex.Replace(command.Trim().ToLower(), @"\s+", " ");

            // 检查是否包含危险字符
            var dangerousPatterns = new[]
            {
                @"[;&|`$()]",  // 命令分隔符和特殊字符
                @">\s*\w+",    // 重定向
                @"<\s*\w+",    // 输入重定向
                @"\|\s*\w+",   // 管道
                @"^\s*del\s+", // 删除命令
                @"^\s*rd\s+",  // 删除目录
                @"^\s*format\s+", // 格式化
                @"^\s*shutdown\s+", // 关机
                @"^\s*reboot\s+",   // 重启
            };

            foreach (var pattern in dangerousPatterns)
            {
                if (Regex.IsMatch(normalizedCommand, pattern))
                    return false;
            }

            // 检查命令是否在允许列表中
            var commandParts = normalizedCommand.Split(' ');
            var mainCommand = commandParts[0];

            return allowedCommands.Any(allowed => 
                mainCommand.StartsWith(allowed.ToLower()) || 
                mainCommand.Equals(allowed.ToLower()));
        }

        private CommandValidationResult ValidateCommand(string command)
        {
            if (string.IsNullOrWhiteSpace(command))
                return new CommandValidationResult(false, "命令不能为空");

            // 检查命令长度
            if (command.Length > 1000)
                return new CommandValidationResult(false, "命令长度超过限制");

            // 规范化命令
            var normalizedCommand = command.Trim().ToLower();

            // 检查危险模式
            foreach (var pattern in DANGEROUS_PATTERNS)
            {
                if (Regex.IsMatch(normalizedCommand, pattern))
                    return new CommandValidationResult(false, $"命令包含危险模式: {pattern}");
            }

            // 检查命令是否在白名单中
            var commandParts = normalizedCommand.Split(' ', StringSplitOptions.RemoveEmptyEntries);
            if (commandParts.Length == 0)
                return new CommandValidationResult(false, "无效的命令格式");

            var mainCommand = commandParts[0];
            if (!ALLOWED_COMMANDS.Contains(mainCommand))
                return new CommandValidationResult(false, $"命令 '{mainCommand}' 不在允许列表中");

            return new CommandValidationResult(true, null);
        }

        private string EscapeCommand(string command)
        {
            // 转义特殊字符以防止注入
            return command.Replace("\"", "\\\"").Replace("'", "\\'");
        }

        private async Task LogSecurityEventAsync(string eventType, string details)
        {
            var auditEntry = $"[SECURITY] {eventType} - User: {Environment.UserName} - Machine: {Environment.MachineName} - Details: {details}";
            await _logger.LogInfoAsync(auditEntry);
        }

        private string SanitizeCommandForLog(string command)
        {
            // 移除可能的敏感信息用于日志记录
            if (string.IsNullOrEmpty(command))
                return command;

            // 隐藏可能的密码或敏感参数
            var sanitized = Regex.Replace(command, @"(password|pwd|pass|key|token|secret)\s*[=:]\s*\S+", 
                "$1=***", RegexOptions.IgnoreCase);
            
            return sanitized;
        }
    }

    public class CommandValidationResult
    {
        public bool IsValid { get; }
        public string ErrorMessage { get; }

        public CommandValidationResult(bool isValid, string errorMessage)
        {
            IsValid = isValid;
            ErrorMessage = errorMessage;
        }
    }
}