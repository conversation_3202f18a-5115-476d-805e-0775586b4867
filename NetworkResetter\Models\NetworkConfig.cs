using System;
using System.Collections.Generic;

namespace NetworkResetter.Models
{
    public class NetworkConfig
    {
        public string AdapterName { get; set; }
        public string IpAddress { get; set; }
        public string SubnetMask { get; set; }
        public string Gateway { get; set; }
        public List<string> DnsServers { get; set; } = new();
        public bool DhcpEnabled { get; set; }
        public string MacAddress { get; set; }
        public string DhcpServer { get; set; }
        public DateTime? DhcpLeaseObtained { get; set; }
        public DateTime? DhcpLeaseExpires { get; set; }
        public DateTime BackupTime { get; set; }
    }

    public class ProxyConfig
    {
        public bool ProxyEnabled { get; set; }
        public string ProxyServer { get; set; }
        public int ProxyPort { get; set; }
        public string ProxyBypass { get; set; }
        public DateTime BackupTime { get; set; }
    }

    public class NetworkDiagnostics
    {
        public bool InternetConnectivity { get; set; }
        public int PingLatency { get; set; }
        public string DnsResolution { get; set; }
        public List<string> ActiveConnections { get; set; } = new();
        public DateTime TestTime { get; set; }
    }
}