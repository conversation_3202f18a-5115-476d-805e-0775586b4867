# NetworkResetter 架构文档

## 项目概述

NetworkResetter 是一个专业的Windows网络环境重置工具，采用现代化的软件架构设计，提供企业级的代码质量和用户体验。

## 技术栈

### 核心技术
- **.NET 7.0** - 现代化的.NET平台
- **WPF (Windows Presentation Foundation)** - 桌面UI框架
- **C#** - 主要编程语言

### 架构模式
- **MVVM (Model-View-ViewModel)** - UI架构模式
- **依赖注入 (Dependency Injection)** - 使用Microsoft.Extensions.DependencyInjection
- **异步编程 (Async/Await)** - 响应式用户界面

### 关键依赖包
```xml
<PackageReference Include="System.Management" Version="7.0.2" />
<PackageReference Include="Microsoft.Win32.Registry" Version="5.0.0" />
<PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="7.0.0" />
<PackageReference Include="Microsoft.Extensions.Hosting" Version="7.0.1" />
<PackageReference Include="Microsoft.VisualBasic" Version="10.3.0" />
```

## 项目结构

```
NetworkResetter/
├── Common/                          # 公共组件
│   ├── Configuration/               # 配置管理
│   │   └── AppConfig.cs            # 应用程序配置
│   ├── DependencyInjection/        # 依赖注入配置
│   │   └── ServiceCollectionExtensions.cs
│   ├── Interfaces/                 # 接口定义
│   │   └── INetworkService.cs      # 服务接口
│   └── UI/                         # UI公共组件
│       └── NotificationService.cs  # 通知服务
├── Converters/                     # 值转换器
│   └── InverseBooleanConverter.cs
├── Models/                         # 数据模型
│   ├── NetworkConfig.cs           # 网络配置模型
│   └── OperationResult.cs         # 操作结果模型
├── Services/                       # 业务逻辑服务
│   ├── BackupService.cs           # 备份服务
│   ├── FirewallService.cs         # 防火墙服务
│   ├── HealthCheckService.cs      # 健康检查服务
│   ├── LoggingService.cs          # 日志服务
│   ├── NetworkService.cs          # 网络服务
│   ├── ProxyService.cs            # 代理服务
│   └── SystemCommandService.cs    # 系统命令服务
├── ViewModels/                     # 视图模型
│   └── MainViewModel.cs           # 主视图模型
├── Views/                          # 视图
│   ├── HelpWindow.xaml            # 帮助窗口
│   ├── MainWindow.xaml            # 主窗口
│   ├── NetworkConfigWindow.xaml   # 网络配置窗口
│   ├── OperationResultDialog.xaml # 操作结果对话框
│   ├── PerformanceMonitorWindow.xaml # 性能监控窗口
│   └── SettingsWindow.xaml        # 设置窗口
├── App.xaml                        # 应用程序入口
└── app.manifest                    # UAC权限配置
```

## 核心架构组件

### 1. 依赖注入容器

使用Microsoft.Extensions.DependencyInjection实现IoC容器：

```csharp
public static IServiceCollection AddNetworkResetterServices(this IServiceCollection services)
{
    // 注册服务
    services.AddSingleton<ILoggingService, LoggingService>();
    services.AddSingleton<ISystemCommandService, SystemCommandService>();
    services.AddScoped<INetworkService, NetworkService>();
    services.AddScoped<IProxyService, ProxyService>();
    services.AddScoped<IFirewallService, FirewallService>();
    services.AddScoped<IBackupService, BackupService>();
    services.AddScoped<HealthCheckService>();

    // 注册ViewModels
    services.AddTransient<MainViewModel>();

    return services;
}
```

### 2. 配置管理系统

集中式配置管理，支持持久化：

```csharp
public class AppConfig
{
    public NetworkDiagnosticConfig NetworkDiagnostic { get; set; } = new();
    public LoggingConfig Logging { get; set; } = new();
    public SecurityConfig Security { get; set; } = new();
    public UIConfig UI { get; set; } = new();
    
    public static AppConfig Instance { get; } // 单例模式
}
```

### 3. 统一的操作结果模型

标准化的操作返回类型：

```csharp
public class OperationResult
{
    public bool Success { get; set; }
    public string Message { get; set; }
    public string ErrorCode { get; set; }
    public Exception Exception { get; set; }
    public List<string> Details { get; set; } = new();
}

public class OperationResult<T> : OperationResult
{
    public T Data { get; set; }
}
```

### 4. 安全的命令执行服务

防止命令注入的安全执行：

```csharp
public class SystemCommandService : ISystemCommandService
{
    public async Task<CommandResult> ExecuteCommandSafeAsync(
        string command, 
        string[] allowedCommands, 
        int timeoutMs = 30000)
    {
        // 命令验证和安全执行
    }
}
```

### 5. 异步日志系统

高性能的异步日志记录：

```csharp
public class LoggingService : ILoggingService, IDisposable
{
    private readonly ConcurrentQueue<LogEntry> _logQueue;
    private readonly Timer _flushTimer;
    
    // 异步队列式日志写入
    // 日志轮转和大小控制
    // 线程安全的并发处理
}
```

## 设计模式应用

### 1. MVVM模式
- **Model**: 数据模型和业务逻辑 (Services层)
- **View**: XAML用户界面
- **ViewModel**: 视图逻辑和数据绑定

### 2. 依赖注入模式
- 接口抽象，降低耦合
- 便于单元测试
- 支持运行时配置

### 3. 单例模式
- 配置管理 (AppConfig)
- 日志服务 (LoggingService)

### 4. 工厂模式
- 服务创建和管理
- 依赖注入容器

### 5. 观察者模式
- INotifyPropertyChanged
- 数据绑定机制

## 安全性设计

### 1. 权限管理
- UAC权限提升
- 管理员权限检查
- 自动提权尝试

### 2. 命令执行安全
- 命令白名单验证
- 参数安全检查
- 超时控制

### 3. 数据保护
- 敏感信息脱敏
- 安全的日志记录
- 配置文件保护

## 性能优化

### 1. 异步编程
- 所有I/O操作异步化
- UI线程不阻塞
- 并发操作支持

### 2. 内存管理
- 正确的资源释放
- WMI对象生命周期管理
- 垃圾回收优化

### 3. 缓存策略
- 配置信息缓存
- 网络状态缓存
- 操作结果缓存

## 测试策略

### 1. 单元测试
- xUnit测试框架
- Moq模拟框架
- FluentAssertions断言库

### 2. 集成测试
- 服务集成测试
- 数据库集成测试
- 外部依赖测试

### 3. UI测试
- 自动化UI测试
- 用户交互测试
- 响应性测试

## 部署和分发

### 1. 编译配置
- Debug和Release配置
- 平台特定编译
- 依赖包管理

### 2. 打包分发
- 独立应用发布
- 安装程序制作
- 自动更新机制

### 3. 监控和维护
- 应用程序遥测
- 错误报告收集
- 性能监控

## 扩展性设计

### 1. 插件架构
- 接口定义标准
- 动态加载机制
- 配置管理支持

### 2. 多语言支持
- 资源文件本地化
- 动态语言切换
- 文化特定格式

### 3. 主题系统
- 可切换的UI主题
- 自定义样式支持
- 响应式设计

## 最佳实践

### 1. 代码质量
- 遵循SOLID原则
- 代码审查流程
- 静态代码分析

### 2. 文档管理
- API文档生成
- 架构文档维护
- 用户手册更新

### 3. 版本控制
- Git工作流程
- 分支管理策略
- 发布标签管理

这个架构设计确保了NetworkResetter具有良好的可维护性、可扩展性和可测试性，同时提供了优秀的用户体验和系统性能。