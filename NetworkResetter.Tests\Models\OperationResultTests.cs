using FluentAssertions;
using NetworkResetter.Models;
using Xunit;

namespace NetworkResetter.Tests.Models
{
    public class OperationResultTests
    {
        [Fact]
        public void CreateSuccess_WithMessage_ReturnsSuccessResult()
        {
            // Arrange
            var message = "操作成功";

            // Act
            var result = OperationResult.CreateSuccess(message);

            // Assert
            result.Success.Should().BeTrue();
            result.Message.Should().Be(message);
            result.ErrorCode.Should().BeNull();
            result.Exception.Should().BeNull();
            result.Details.Should().BeEmpty();
        }

        [Fact]
        public void CreateFailure_WithMessageAndErrorCode_ReturnsFailureResult()
        {
            // Arrange
            var message = "操作失败";
            var errorCode = "TEST_ERROR";
            var exception = new Exception("测试异常");

            // Act
            var result = OperationResult.CreateFailure(message, errorCode, exception);

            // Assert
            result.Success.Should().BeFalse();
            result.Message.Should().Be(message);
            result.ErrorCode.Should().Be(errorCode);
            result.Exception.Should().Be(exception);
        }

        [Fact]
        public void AddDetail_AddsDetailToList()
        {
            // Arrange
            var result = OperationResult.CreateSuccess();
            var detail = "详细信息";

            // Act
            var returnedResult = result.AddDetail(detail);

            // Assert
            returnedResult.Should().BeSameAs(result); // 应该返回同一个实例
            result.Details.Should().Contain(detail);
        }

        [Fact]
        public void OperationResultGeneric_CreateSuccess_WithData_ReturnsSuccessWithData()
        {
            // Arrange
            var data = "测试数据";
            var message = "操作成功";

            // Act
            var result = OperationResult<string>.CreateSuccess(data, message);

            // Assert
            result.Success.Should().BeTrue();
            result.Message.Should().Be(message);
            result.Data.Should().Be(data);
        }

        [Fact]
        public void OperationResultGeneric_CreateFailure_ReturnsFailureWithoutData()
        {
            // Arrange
            var message = "操作失败";

            // Act
            var result = OperationResult<string>.CreateFailure(message);

            // Assert
            result.Success.Should().BeFalse();
            result.Message.Should().Be(message);
            result.Data.Should().BeNull();
        }
    }
}