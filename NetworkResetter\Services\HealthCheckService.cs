using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using NetworkResetter.Common.Interfaces;
using NetworkResetter.Models;

namespace NetworkResetter.Services
{
    public class HealthCheckService
    {
        private readonly ILoggingService _logger;

        public HealthCheckService(ILoggingService logger)
        {
            _logger = logger;
        }

        public async Task<HealthCheckResult> RunHealthCheckAsync()
        {
            var result = new HealthCheckResult
            {
                CheckTime = DateTime.Now,
                Checks = new List<HealthCheck>()
            };

            try
            {
                await _logger.LogInfoAsync("开始系统健康检查");

                // 检查管理员权限
                result.Checks.Add(await CheckAdministratorPrivilegesAsync());

                // 检查磁盘空间
                result.Checks.Add(await CheckDiskSpaceAsync());

                // 检查内存使用
                result.Checks.Add(await CheckMemoryUsageAsync());

                // 检查网络服务状态
                result.Checks.Add(await CheckNetworkServicesAsync());

                // 检查日志目录
                result.Checks.Add(await CheckLogDirectoryAsync());

                // 计算总体健康状态
                var failedChecks = 0;
                var warningChecks = 0;

                foreach (var check in result.Checks)
                {
                    switch (check.Status)
                    {
                        case HealthStatus.Failed:
                            failedChecks++;
                            break;
                        case HealthStatus.Warning:
                            warningChecks++;
                            break;
                    }
                }

                if (failedChecks > 0)
                {
                    result.OverallStatus = HealthStatus.Failed;
                    result.Summary = $"系统健康检查失败 ({failedChecks} 个严重问题)";
                }
                else if (warningChecks > 0)
                {
                    result.OverallStatus = HealthStatus.Warning;
                    result.Summary = $"系统健康检查警告 ({warningChecks} 个警告)";
                }
                else
                {
                    result.OverallStatus = HealthStatus.Healthy;
                    result.Summary = "系统健康状态良好";
                }

                await _logger.LogInfoAsync($"健康检查完成: {result.Summary}");
            }
            catch (Exception ex)
            {
                await _logger.LogErrorAsync("健康检查异常", ex);
                result.OverallStatus = HealthStatus.Failed;
                result.Summary = $"健康检查异常: {ex.Message}";
            }

            return result;
        }

        private Task<HealthCheck> CheckAdministratorPrivilegesAsync()
        {
            try
            {
                var identity = System.Security.Principal.WindowsIdentity.GetCurrent();
                var principal = new System.Security.Principal.WindowsPrincipal(identity);
                var isAdmin = principal.IsInRole(System.Security.Principal.WindowsBuiltInRole.Administrator);

                var result = new HealthCheck
                {
                    Name = "管理员权限",
                    Status = isAdmin ? HealthStatus.Healthy : HealthStatus.Failed,
                    Message = isAdmin ? "具有管理员权限" : "缺少管理员权限",
                    Details = $"当前用户: {identity.Name}"
                };
                
                return Task.FromResult(result);
            }
            catch (Exception ex)
            {
                var result = new HealthCheck
                {
                    Name = "管理员权限",
                    Status = HealthStatus.Failed,
                    Message = "权限检查失败",
                    Details = ex.Message
                };
                
                return Task.FromResult(result);
            }
        }

        private Task<HealthCheck> CheckDiskSpaceAsync()
        {
            try
            {
                var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
                var drive = new DriveInfo(Path.GetPathRoot(appDataPath));
                
                var freeSpaceGB = drive.AvailableFreeSpace / (1024 * 1024 * 1024);
                var totalSpaceGB = drive.TotalSize / (1024 * 1024 * 1024);
                var usagePercent = (double)(drive.TotalSize - drive.AvailableFreeSpace) / drive.TotalSize * 100;

                HealthStatus status;
                string message;

                if (freeSpaceGB < 1)
                {
                    status = HealthStatus.Failed;
                    message = "磁盘空间严重不足";
                }
                else if (usagePercent > 90)
                {
                    status = HealthStatus.Warning;
                    message = "磁盘空间不足";
                }
                else
                {
                    status = HealthStatus.Healthy;
                    message = "磁盘空间充足";
                }

                var result = new HealthCheck
                {
                    Name = "磁盘空间",
                    Status = status,
                    Message = message,
                    Details = $"可用空间: {freeSpaceGB:F1}GB / {totalSpaceGB:F1}GB ({100 - usagePercent:F1}% 可用)"
                };
                
                return Task.FromResult(result);
            }
            catch (Exception ex)
            {
                var result = new HealthCheck
                {
                    Name = "磁盘空间",
                    Status = HealthStatus.Failed,
                    Message = "磁盘空间检查失败",
                    Details = ex.Message
                };
                
                return Task.FromResult(result);
            }
        }

        private Task<HealthCheck> CheckMemoryUsageAsync()
        {
            try
            {
                var process = Process.GetCurrentProcess();
                var memoryMB = process.WorkingSet64 / (1024 * 1024);

                HealthStatus status;
                string message;

                if (memoryMB > 500)
                {
                    status = HealthStatus.Warning;
                    message = "内存使用较高";
                }
                else if (memoryMB > 1000)
                {
                    status = HealthStatus.Failed;
                    message = "内存使用过高";
                }
                else
                {
                    status = HealthStatus.Healthy;
                    message = "内存使用正常";
                }

                var result = new HealthCheck
                {
                    Name = "内存使用",
                    Status = status,
                    Message = message,
                    Details = $"当前使用: {memoryMB}MB"
                };
                
                return Task.FromResult(result);
            }
            catch (Exception ex)
            {
                var result = new HealthCheck
                {
                    Name = "内存使用",
                    Status = HealthStatus.Failed,
                    Message = "内存检查失败",
                    Details = ex.Message
                };
                
                return Task.FromResult(result);
            }
        }

        private Task<HealthCheck> CheckNetworkServicesAsync()
        {
            try
            {
                var services = new[] { "Dhcp", "Dnscache", "Netman" };
                var runningServices = 0;
                var details = new List<string>();

                foreach (var serviceName in services)
                {
                    try
                    {
                        using var service = new System.ServiceProcess.ServiceController(serviceName);
                        var isRunning = service.Status == System.ServiceProcess.ServiceControllerStatus.Running;
                        
                        if (isRunning) runningServices++;
                        details.Add($"{serviceName}: {(isRunning ? "运行中" : "已停止")}");
                    }
                    catch
                    {
                        details.Add($"{serviceName}: 检查失败");
                    }
                }

                HealthStatus status;
                string message;

                if (runningServices == services.Length)
                {
                    status = HealthStatus.Healthy;
                    message = "网络服务正常";
                }
                else if (runningServices > 0)
                {
                    status = HealthStatus.Warning;
                    message = "部分网络服务异常";
                }
                else
                {
                    status = HealthStatus.Failed;
                    message = "网络服务异常";
                }

                var result = new HealthCheck
                {
                    Name = "网络服务",
                    Status = status,
                    Message = message,
                    Details = string.Join("; ", details)
                };
                
                return Task.FromResult(result);
            }
            catch (Exception ex)
            {
                var result = new HealthCheck
                {
                    Name = "网络服务",
                    Status = HealthStatus.Failed,
                    Message = "网络服务检查失败",
                    Details = ex.Message
                };
                
                return Task.FromResult(result);
            }
        }

        private async Task<HealthCheck> CheckLogDirectoryAsync()
        {
            try
            {
                var logDirectory = Path.Combine(
                    Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                    "NetworkResetter", "Logs");

                var canWrite = true;
                var errorMessage = "";

                try
                {
                    Directory.CreateDirectory(logDirectory);
                    var testFile = Path.Combine(logDirectory, "test.tmp");
                    await File.WriteAllTextAsync(testFile, "test");
                    File.Delete(testFile);
                }
                catch (Exception ex)
                {
                    canWrite = false;
                    errorMessage = ex.Message;
                }

                return new HealthCheck
                {
                    Name = "日志目录",
                    Status = canWrite ? HealthStatus.Healthy : HealthStatus.Failed,
                    Message = canWrite ? "日志目录可写" : "日志目录不可写",
                    Details = canWrite ? $"路径: {logDirectory}" : $"错误: {errorMessage}"
                };
            }
            catch (Exception ex)
            {
                return new HealthCheck
                {
                    Name = "日志目录",
                    Status = HealthStatus.Failed,
                    Message = "日志目录检查失败",
                    Details = ex.Message
                };
            }
        }
    }

    public class HealthCheckResult
    {
        public DateTime CheckTime { get; set; }
        public HealthStatus OverallStatus { get; set; }
        public string Summary { get; set; }
        public List<HealthCheck> Checks { get; set; } = new();
    }

    public class HealthCheck
    {
        public string Name { get; set; }
        public HealthStatus Status { get; set; }
        public string Message { get; set; }
        public string Details { get; set; }
    }

    public enum HealthStatus
    {
        Healthy,
        Warning,
        Failed
    }
}