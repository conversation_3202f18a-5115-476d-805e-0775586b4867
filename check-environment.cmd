@echo off
chcp 65001 >nul
echo NetworkResetter - Environment Check
echo ====================================

echo Checking Windows version...
ver

echo.
echo Checking .NET SDK...
dotnet --version
if %errorlevel% neq 0 (
    echo ERROR: .NET SDK not found
    echo Please install .NET 7.0 SDK from: https://dotnet.microsoft.com/download/dotnet/7.0
    goto :error
)

echo.
echo Available .NET SDKs:
dotnet --list-sdks

echo.
echo Available .NET Runtimes:
dotnet --list-runtimes

echo.
echo Checking for required .NET 7.0...
dotnet --list-runtimes | findstr "Microsoft.WindowsDesktop.App 7."
if %errorlevel% neq 0 (
    echo WARNING: .NET 7.0 Windows Desktop Runtime not found
    echo This is required for running the application
    echo Download from: https://dotnet.microsoft.com/download/dotnet/7.0
)

echo.
echo Checking administrator privileges...
net session >nul 2>&1
if %errorlevel% equ 0 (
    echo SUCCESS: Running with administrator privileges
) else (
    echo WARNING: Not running with administrator privileges
    echo The application requires administrator privileges to function properly
)

echo.
echo Checking system architecture...
if "%PROCESSOR_ARCHITECTURE%"=="AMD64" (
    echo SUCCESS: 64-bit system detected
) else (
    echo WARNING: 32-bit system detected
    echo This application is optimized for 64-bit systems
)

echo.
echo ====================================
echo Environment check completed
echo ====================================
goto :end

:error
echo.
echo ====================================
echo Environment check failed!
echo Please install the required components
echo ====================================

:end
pause