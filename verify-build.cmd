@echo off
chcp 65001 >nul
echo NetworkResetter - Build Verification Script
echo ============================================

echo Checking .NET SDK...
dotnet --version
if %errorlevel% neq 0 (
    echo ERROR: .NET SDK not found
    pause
    exit /b 1
)

echo.
echo Cleaning project...
dotnet clean NetworkResetter.sln
if exist "NetworkResetter\bin" rmdir /s /q "NetworkResetter\bin" 2>nul
if exist "NetworkResetter\obj" rmdir /s /q "NetworkResetter\obj" 2>nul
if exist "NetworkResetter.Tests\bin" rmdir /s /q "NetworkResetter.Tests\bin" 2>nul
if exist "NetworkResetter.Tests\obj" rmdir /s /q "NetworkResetter.Tests\obj" 2>nul

echo.
echo Restoring NuGet packages...
dotnet restore NetworkResetter.sln
if %errorlevel% neq 0 (
    echo ERROR: Package restore failed
    pause
    exit /b 1
)

echo.
echo Building project (Debug)...
dotnet build NetworkResetter.sln -c Debug --verbosity normal
if %errorlevel% neq 0 (
    echo ERROR: Debug build failed
    pause
    exit /b 1
)

echo.
echo Building project (Release)...
dotnet build NetworkResetter.sln -c Release --verbosity normal
if %errorlevel% neq 0 (
    echo ERROR: Release build failed
    pause
    exit /b 1
)

echo.
echo Running unit tests...
dotnet test NetworkResetter.Tests -c Release --no-build --verbosity normal
if %errorlevel% neq 0 (
    echo WARNING: Some tests failed
)

echo.
echo ============================================
echo Build verification completed!
echo All projects built successfully
echo ============================================
pause