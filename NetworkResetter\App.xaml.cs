using System;
using System.Diagnostics;
using System.Security.Principal;
using System.Windows;
using System.Windows.Threading;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using NetworkResetter.Common.DependencyInjection;
using NetworkResetter.Common.Interfaces;
using NetworkResetter.Views;

namespace NetworkResetter
{
    public partial class App : Application
    {
        private IHost _host;
        private ILoggingService _logger;

        protected override async void OnStartup(StartupEventArgs e)
        {
            try
            {
                // 检查管理员权限
                if (!IsRunAsAdministrator())
                {
                    var result = MessageBox.Show(
                        "此应用程序需要管理员权限才能正常运行。\n\n是否尝试以管理员身份重新启动？", 
                        "权限不足", 
                        MessageBoxButton.YesNo, 
                        MessageBoxImage.Warning);

                    if (result == MessageBoxResult.Yes)
                    {
                        RestartAsAdministrator();
                    }
                    
                    Shutdown();
                    return;
                }

                // 构建依赖注入容器
                _host = Host.CreateDefaultBuilder()
                    .ConfigureServices((context, services) =>
                    {
                        services.AddNetworkResetterServices();
                    })
                    .Build();

                await _host.StartAsync();

                // 获取日志服务
                _logger = _host.Services.GetRequiredService<ILoggingService>();
                await _logger.LogInfoAsync("应用程序启动");

                // 全局异常处理
                DispatcherUnhandledException += OnDispatcherUnhandledException;
                AppDomain.CurrentDomain.UnhandledException += OnUnhandledException;

                // 创建主窗口
                var mainWindow = new MainWindow(_host.Services);
                mainWindow.Show();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"应用程序启动失败：\n{ex.Message}", 
                    "启动错误", MessageBoxButton.OK, MessageBoxImage.Error);
                Shutdown();
            }
        }

        protected override async void OnExit(ExitEventArgs e)
        {
            try
            {
                if (_logger != null)
                {
                    await _logger.LogInfoAsync("应用程序退出");
                }

                if (_host != null)
                {
                    await _host.StopAsync();
                    _host.Dispose();
                }
            }
            catch
            {
                // 退出时忽略异常
            }

            base.OnExit(e);
        }

        private bool IsRunAsAdministrator()
        {
            try
            {
                var identity = WindowsIdentity.GetCurrent();
                var principal = new WindowsPrincipal(identity);
                return principal.IsInRole(WindowsBuiltInRole.Administrator);
            }
            catch
            {
                return false;
            }
        }

        private void RestartAsAdministrator()
        {
            try
            {
                var processInfo = new ProcessStartInfo
                {
                    FileName = Environment.ProcessPath,
                    UseShellExecute = true,
                    Verb = "runas" // 以管理员身份运行
                };

                Process.Start(processInfo);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"无法以管理员身份启动：\n{ex.Message}", 
                    "启动失败", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void OnDispatcherUnhandledException(object sender, DispatcherUnhandledExceptionEventArgs e)
        {
            try
            {
                if (_logger != null)
                {
                    await _logger.LogErrorAsync("未处理的UI异常", e.Exception);
                }

                var message = $"应用程序发生未处理的异常：\n{e.Exception.Message}\n\n详细信息已记录到日志文件。";
                MessageBox.Show(message, "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                
                e.Handled = true;
            }
            catch
            {
                // 异常处理失败，显示基本错误信息
                MessageBox.Show($"应用程序发生严重错误：\n{e.Exception.Message}", 
                    "严重错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void OnUnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            try
            {
                var exception = e.ExceptionObject as Exception;
                
                if (_logger != null && exception != null)
                {
                    await _logger.LogErrorAsync("未处理的应用程序异常", exception);
                }

                var message = $"应用程序发生严重错误：\n{exception?.Message}\n\n详细信息已记录到日志文件。";
                MessageBox.Show(message, "严重错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            catch
            {
                // 最后的异常处理
                var exception = e.ExceptionObject as Exception;
                MessageBox.Show($"应用程序发生不可恢复的错误：\n{exception?.Message}", 
                    "致命错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}