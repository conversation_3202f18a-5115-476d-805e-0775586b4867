@echo off
chcp 65001 >nul
echo NetworkResetter - Multi-Target Build Script
echo =============================================

echo Checking .NET SDK...
dotnet --version
if %errorlevel% neq 0 (
    echo ERROR: .NET SDK not found
    pause
    exit /b 1
)

echo.
echo Checking available SDKs...
dotnet --list-sdks

echo.
echo Cleaning project...
dotnet clean NetworkResetter.sln

echo.
echo Restoring packages...
dotnet restore NetworkResetter.sln
if %errorlevel% neq 0 (
    echo ERROR: Package restore failed
    pause
    exit /b 1
)

echo.
echo Building Release version...
dotnet build NetworkResetter.sln -c Release
if %errorlevel% neq 0 (
    echo ERROR: Build failed
    pause
    exit /b 1
)

echo.
echo Running tests...
dotnet test NetworkResetter.Tests -c Release --no-build
if %errorlevel% neq 0 (
    echo WARNING: Some tests failed
)

echo.
echo Publishing for different targets...

echo Publishing framework-dependent...
dotnet publish NetworkResetter -c Release -o publish\framework-dependent
if %errorlevel% neq 0 (
    echo ERROR: Framework-dependent publish failed
    goto :error
)

echo Publishing self-contained (win-x64)...
dotnet publish NetworkResetter -c Release -r win-x64 --self-contained -o publish\win-x64
if %errorlevel% neq 0 (
    echo ERROR: Self-contained publish failed
    goto :error
)

echo Publishing single-file (win-x64)...
dotnet publish NetworkResetter -c Release -r win-x64 --self-contained -p:PublishSingleFile=true -o publish\single-file
if %errorlevel% neq 0 (
    echo ERROR: Single-file publish failed
    goto :error
)

echo.
echo =============================================
echo Multi-target build completed successfully!
echo.
echo Output locations:
echo - Framework-dependent: publish\framework-dependent\
echo - Self-contained: publish\win-x64\
echo - Single-file: publish\single-file\
echo =============================================
goto :end

:error
echo.
echo =============================================
echo Build failed! Check the error messages above.
echo =============================================

:end
pause