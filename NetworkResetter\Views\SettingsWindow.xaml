<Window x:Class="NetworkResetter.Views.SettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="高级设置" Height="500" Width="600"
        WindowStartupLocation="CenterOwner">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" Text="高级设置" 
                   FontSize="18" FontWeight="Bold" 
                   Margin="0,0,0,20"/>

        <!-- 设置选项卡 -->
        <TabControl Grid.Row="1">
            <!-- 网络诊断设置 -->
            <TabItem Header="网络诊断">
                <ScrollViewer Margin="15">
                    <StackPanel>
                        <GroupBox Header="Ping测试设置" Margin="0,0,0,15">
                            <Grid Margin="10">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                
                                <TextBlock Grid.Row="0" Text="测试主机 (每行一个):" Margin="0,0,0,5"/>
                                <TextBox x:Name="PingHostsTextBox" Grid.Row="1" 
                                         Height="80" AcceptsReturn="True" 
                                         TextWrapping="Wrap" VerticalScrollBarVisibility="Auto"/>
                                
                                <StackPanel Grid.Row="2" Orientation="Horizontal" Margin="0,10,0,0">
                                    <TextBlock Text="超时时间(ms):" VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    <TextBox x:Name="PingTimeoutTextBox" Width="80" Text="5000"/>
                                </StackPanel>
                            </Grid>
                        </GroupBox>

                        <GroupBox Header="DNS测试设置" Margin="0,0,0,15">
                            <Grid Margin="10">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                
                                <TextBlock Grid.Row="0" Text="测试域名 (每行一个):" Margin="0,0,0,5"/>
                                <TextBox x:Name="DnsTestSitesTextBox" Grid.Row="1" 
                                         Height="80" AcceptsReturn="True" 
                                         TextWrapping="Wrap" VerticalScrollBarVisibility="Auto"/>
                                
                                <StackPanel Grid.Row="2" Orientation="Horizontal" Margin="0,10,0,0">
                                    <TextBlock Text="超时时间(ms):" VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    <TextBox x:Name="DnsTimeoutTextBox" Width="80" Text="10000"/>
                                </StackPanel>
                            </Grid>
                        </GroupBox>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- 日志设置 -->
            <TabItem Header="日志">
                <ScrollViewer Margin="15">
                    <StackPanel>
                        <GroupBox Header="日志级别" Margin="0,0,0,15">
                            <StackPanel Margin="10">
                                <RadioButton x:Name="LogLevelInfoRadio" Content="信息 (Info)" IsChecked="True" Margin="0,5"/>
                                <RadioButton x:Name="LogLevelWarningRadio" Content="警告 (Warning)" Margin="0,5"/>
                                <RadioButton x:Name="LogLevelErrorRadio" Content="错误 (Error)" Margin="0,5"/>
                            </StackPanel>
                        </GroupBox>

                        <GroupBox Header="日志文件管理" Margin="0,0,0,15">
                            <Grid Margin="10">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                
                                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,5">
                                    <TextBlock Text="最大文件大小(MB):" VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    <TextBox x:Name="MaxLogFileSizeTextBox" Width="80" Text="10"/>
                                </StackPanel>
                                
                                <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,5">
                                    <TextBlock Text="保留文件数量:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    <TextBox x:Name="MaxLogFilesTextBox" Width="80" Text="5"/>
                                </StackPanel>
                                
                                <CheckBox x:Name="EnableConsoleLoggingCheckBox" Grid.Row="2" 
                                          Content="启用控制台日志" Margin="0,10,0,0"/>
                            </Grid>
                        </GroupBox>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- 安全设置 -->
            <TabItem Header="安全">
                <ScrollViewer Margin="15">
                    <StackPanel>
                        <GroupBox Header="命令执行安全" Margin="0,0,0,15">
                            <Grid Margin="10">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                
                                <CheckBox x:Name="EnableCommandValidationCheckBox" Grid.Row="0" 
                                          Content="启用命令验证" IsChecked="True" Margin="0,5"/>
                                
                                <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,10">
                                    <TextBlock Text="命令超时(ms):" VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    <TextBox x:Name="CommandTimeoutTextBox" Width="80" Text="30000"/>
                                </StackPanel>
                                
                                <TextBlock Grid.Row="2" Text="允许的命令 (每行一个):" Margin="0,10,0,5"/>
                            </Grid>
                        </GroupBox>

                        <TextBox x:Name="AllowedCommandsTextBox" Height="100" 
                                 AcceptsReturn="True" TextWrapping="Wrap" 
                                 VerticalScrollBarVisibility="Auto"/>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- 界面设置 -->
            <TabItem Header="界面">
                <ScrollViewer Margin="15">
                    <StackPanel>
                        <GroupBox Header="主题设置" Margin="0,0,0,15">
                            <StackPanel Margin="10">
                                <RadioButton x:Name="LightThemeRadio" Content="浅色主题" IsChecked="True" Margin="0,5"/>
                                <RadioButton x:Name="DarkThemeRadio" Content="深色主题" Margin="0,5"/>
                            </StackPanel>
                        </GroupBox>

                        <GroupBox Header="操作设置" Margin="0,0,0,15">
                            <Grid Margin="10">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                
                                <CheckBox x:Name="ShowAdvancedOptionsCheckBox" Grid.Row="0" 
                                          Content="显示高级选项" Margin="0,5"/>
                                
                                <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,10">
                                    <TextBlock Text="操作超时(ms):" VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    <TextBox x:Name="OperationTimeoutTextBox" Width="80" Text="60000"/>
                                </StackPanel>
                            </Grid>
                        </GroupBox>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>
        </TabControl>

        <!-- 按钮 -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" 
                    HorizontalAlignment="Right" Margin="0,20,0,0">
            <Button Content="恢复默认" Click="RestoreDefaults_Click" 
                    Width="80" Margin="0,0,10,0"/>
            <Button Content="应用" Click="Apply_Click" 
                    Width="80" Margin="0,0,10,0"/>
            <Button Content="确定" Click="OK_Click" 
                    Width="80" Margin="0,0,10,0" IsDefault="True"/>
            <Button Content="取消" Click="Cancel_Click" 
                    Width="80"/>
        </StackPanel>
    </Grid>
</Window>