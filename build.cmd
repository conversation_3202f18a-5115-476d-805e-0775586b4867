@echo off
chcp 65001 >nul
echo NetworkResetter - Build Script
echo ================================

echo Checking .NET SDK...
dotnet --version
if %errorlevel% neq 0 (
    echo ERROR: .NET SDK not found, please install .NET 7.0 SDK first
    pause
    exit /b 1
)

echo.
echo Restoring NuGet packages...
dotnet restore NetworkResetter.sln
if %errorlevel% neq 0 (
    echo ERROR: Package restore failed
    pause
    exit /b 1
)

echo.
echo Building project (Debug)...
dotnet build NetworkResetter.sln -c Debug
if %errorlevel% neq 0 (
    echo ERROR: Debug build failed
    pause
    exit /b 1
)

echo.
echo Building project (Release)...
dotnet build NetworkResetter.sln -c Release
if %errorlevel% neq 0 (
    echo ERROR: Release build failed
    pause
    exit /b 1
)

echo.
echo Running unit tests...
dotnet test NetworkResetter.Tests -c Release --no-build --verbosity normal
if %errorlevel% neq 0 (
    echo WARNING: Some tests failed, but continuing build process
)

echo.
echo Publishing standalone application...
dotnet publish NetworkResetter -c Release -r win-x64 --self-contained -p:PublishSingleFile=true -o publish
if %errorlevel% neq 0 (
    echo ERROR: Publish failed
    pause
    exit /b 1
)

echo.
echo ================================
echo Build completed successfully!
echo Debug build: NetworkResetter\bin\Debug\net7.0-windows\
echo Release build: NetworkResetter\bin\Release\net7.0-windows\
echo Standalone app: publish\NetworkResetter.exe
echo Test project: NetworkResetter.Tests\bin\Release\net7.0\
echo ================================
pause