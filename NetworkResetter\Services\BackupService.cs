using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using NetworkResetter.Common.Interfaces;
using NetworkResetter.Models;

namespace NetworkResetter.Services
{
    public class BackupService : IBackupService
    {
        private readonly string _backupDirectory;
        private readonly INetworkService _networkService;
        private readonly IProxyService _proxyService;
        private readonly ILoggingService _logger;

        public BackupService(INetworkService networkService, IProxyService proxyService, ILoggingService logger)
        {
            _backupDirectory = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "NetworkResetter", "Backups");
            Directory.CreateDirectory(_backupDirectory);
            _networkService = networkService;
            _proxyService = proxyService;
            _logger = logger;
        }

        public async Task<OperationResult<string>> CreateBackupAsync()
        {
            try
            {
                await _logger.LogInfoAsync("开始创建网络配置备份");
                
                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                var backupFileName = $"network_backup_{timestamp}.json";
                var backupPath = Path.Combine(_backupDirectory, backupFileName);

                // 获取当前配置
                var proxyConfig = await _proxyService.GetCurrentProxyConfigAsync();
                var networkConfigs = await _networkService.GetNetworkConfigsAsync();

                var backup = new
                {
                    Timestamp = DateTime.Now,
                    Version = "1.0",
                    ProxyConfig = proxyConfig,
                    NetworkConfigs = networkConfigs,
                    BackupInfo = new
                    {
                        CreatedBy = "NetworkResetter",
                        MachineName = Environment.MachineName,
                        UserName = Environment.UserName
                    }
                };

                var options = new JsonSerializerOptions 
                { 
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                };
                
                var json = JsonSerializer.Serialize(backup, options);
                await File.WriteAllTextAsync(backupPath, json);

                await _logger.LogInfoAsync($"备份创建成功: {backupPath}");
                
                return OperationResult<string>.CreateSuccess(backupPath, "网络配置备份创建成功")
                    .AddDetail($"备份文件: {backupFileName}")
                    .AddDetail($"代理配置: {(proxyConfig.ProxyEnabled ? "已启用" : "已禁用")}")
                    .AddDetail($"网络适配器: {networkConfigs.Count} 个");
            }
            catch (UnauthorizedAccessException ex)
            {
                await _logger.LogErrorAsync("创建备份权限不足", ex);
                return OperationResult<string>.CreateFailure("权限不足，无法创建备份文件", "ACCESS_DENIED", ex);
            }
            catch (Exception ex)
            {
                await _logger.LogErrorAsync("创建备份失败", ex);
                return OperationResult<string>.CreateFailure("创建备份时发生异常", "BACKUP_CREATE_EXCEPTION", ex);
            }
        }

        public async Task<List<string>> GetAvailableBackupsAsync()
        {
            return await Task.Run(() =>
            {
                try
                {
                    var backupFiles = Directory.GetFiles(_backupDirectory, "network_backup_*.json");
                    var backupList = new List<string>();

                    foreach (var file in backupFiles)
                    {
                        var fileName = Path.GetFileNameWithoutExtension(file);
                        var timestamp = fileName.Replace("network_backup_", "");
                        if (DateTime.TryParseExact(timestamp, "yyyyMMdd_HHmmss", null, System.Globalization.DateTimeStyles.None, out var date))
                        {
                            backupList.Add($"{date:yyyy-MM-dd HH:mm:ss} - {Path.GetFileName(file)}");
                        }
                    }

                    backupList.Sort((x, y) => string.Compare(y, x)); // 最新的在前
                    return backupList;
                }
                catch (Exception ex)
                {
                    _logger.LogErrorAsync("获取备份列表失败", ex).Wait();
                    return new List<string>();
                }
            });
        }

        public async Task<OperationResult> RestoreBackupAsync(string backupFileName)
        {
            try
            {
                await _logger.LogInfoAsync($"开始恢复备份: {backupFileName}");
                
                var actualFileName = backupFileName.Contains(" - ") ? 
                    backupFileName.Split(" - ")[1] : backupFileName;
                var backupPath = Path.Combine(_backupDirectory, actualFileName);
                
                if (!File.Exists(backupPath)) 
                {
                    await _logger.LogWarningAsync($"备份文件不存在: {backupPath}");
                    return OperationResult.CreateFailure("备份文件不存在", "FILE_NOT_FOUND");
                }

                var json = await File.ReadAllTextAsync(backupPath);
                var backup = JsonSerializer.Deserialize<JsonElement>(json);

                var result = OperationResult.CreateSuccess("备份恢复完成");
                var restoredItems = 0;

                // 恢复代理设置
                if (backup.TryGetProperty("proxyConfig", out var proxyElement) || 
                    backup.TryGetProperty("ProxyConfig", out proxyElement))
                {
                    try
                    {
                        var proxyConfig = JsonSerializer.Deserialize<ProxyConfig>(proxyElement.GetRawText());
                        var proxyResult = await _proxyService.RestoreProxySettingsAsync(proxyConfig);
                        
                        if (proxyResult.Success)
                        {
                            result.AddDetail("✓ 代理配置恢复成功");
                            restoredItems++;
                        }
                        else
                        {
                            result.AddDetail($"✗ 代理配置恢复失败: {proxyResult.Message}");
                        }
                    }
                    catch (Exception proxyEx)
                    {
                        await _logger.LogWarningAsync($"代理配置恢复异常: {proxyEx.Message}");
                        result.AddDetail($"✗ 代理配置恢复异常: {proxyEx.Message}");
                    }
                }

                // TODO: 恢复网络配置（需要实现网络配置的恢复逻辑）
                if (backup.TryGetProperty("networkConfigs", out var networkElement) || 
                    backup.TryGetProperty("NetworkConfigs", out networkElement))
                {
                    result.AddDetail("ℹ 网络配置恢复功能待实现");
                }

                if (restoredItems == 0)
                {
                    result.Success = false;
                    result.Message = "没有可恢复的配置项";
                }
                else
                {
                    result.Message = $"备份恢复完成 ({restoredItems} 项)";
                }

                await _logger.LogInfoAsync($"备份恢复操作完成，成功项目: {restoredItems}");
                return result;
            }
            catch (JsonException ex)
            {
                await _logger.LogErrorAsync("备份文件格式错误", ex);
                return OperationResult.CreateFailure("备份文件格式错误", "INVALID_BACKUP_FORMAT", ex);
            }
            catch (Exception ex)
            {
                await _logger.LogErrorAsync("恢复备份失败", ex);
                return OperationResult.CreateFailure("恢复备份时发生异常", "BACKUP_RESTORE_EXCEPTION", ex);
            }
        }


    }
}