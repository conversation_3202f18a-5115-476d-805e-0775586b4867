using System.Windows;
using System.Windows.Media;
using NetworkResetter.Models;

namespace NetworkResetter.Views
{
    public partial class OperationResultDialog : Window
    {
        private OperationResult _result;

        public OperationResultDialog(OperationResult result, string operationName)
        {
            InitializeComponent();
            _result = result;
            ShowResult(result, operationName);
        }

        private void ShowResult(OperationResult result, string operationName)
        {
            OperationNameText.Text = operationName;

            if (result.Success)
            {
                StatusIndicator.Fill = new SolidColorBrush(Colors.Green);
                StatusText.Text = "操作成功";
                StatusText.Foreground = new SolidColorBrush(Colors.Green);
            }
            else
            {
                StatusIndicator.Fill = new SolidColorBrush(Colors.Red);
                StatusText.Text = "操作失败";
                StatusText.Foreground = new SolidColorBrush(Colors.Red);
            }

            // 构建详细信息
            var details = new System.Text.StringBuilder();
            details.AppendLine($"状态: {(result.Success ? "成功" : "失败")}");
            details.AppendLine($"消息: {result.Message}");
            
            if (!string.IsNullOrEmpty(result.ErrorCode))
            {
                details.AppendLine($"错误代码: {result.ErrorCode}");
            }

            if (result.Details.Count > 0)
            {
                details.AppendLine("\n详细步骤:");
                foreach (var detail in result.Details)
                {
                    details.AppendLine($"  {detail}");
                }
            }

            if (result.Exception != null)
            {
                details.AppendLine($"\n异常信息:");
                details.AppendLine($"  {result.Exception.Message}");
                
                if (result.Exception.InnerException != null)
                {
                    details.AppendLine($"  内部异常: {result.Exception.InnerException.Message}");
                }
            }

            DetailsText.Text = details.ToString();
        }

        private void OK_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = true;
            Close();
        }

        private void CopyDetails_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                Clipboard.SetText(DetailsText.Text);
                MessageBox.Show("详细信息已复制到剪贴板", "复制成功", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch
            {
                MessageBox.Show("复制失败", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}