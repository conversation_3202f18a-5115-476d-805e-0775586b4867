using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;

namespace NetworkResetter.Common.Configuration
{
    public class AppConfig
    {
        public NetworkDiagnosticConfig NetworkDiagnostic { get; set; } = new();
        public LoggingConfig Logging { get; set; } = new();
        public SecurityConfig Security { get; set; } = new();
        public UIConfig UI { get; set; } = new();

        private static AppConfig _instance;
        private static readonly object _lock = new object();

        public static AppConfig Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = LoadConfig();
                        }
                    }
                }
                return _instance;
            }
        }

        private static AppConfig LoadConfig()
        {
            try
            {
                var configPath = GetConfigPath();

                if (File.Exists(configPath))
                {
                    // 检查文件权限
                    var fileInfo = new FileInfo(configPath);
                    if (!IsFileSecure(fileInfo))
                    {
                        // 如果文件权限不安全，使用默认配置
                        return new AppConfig();
                    }

                    var json = File.ReadAllText(configPath);
                    var config = JsonSerializer.Deserialize<AppConfig>(json);
                    
                    // 验证配置
                    if (ValidateConfig(config))
                    {
                        return config;
                    }
                }
            }
            catch (Exception ex)
            {
                // 记录配置加载错误
                System.Diagnostics.Debug.WriteLine($"配置加载失败: {ex.Message}");
            }

            return new AppConfig();
        }

        private static string GetConfigPath()
        {
            var configDir = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                "NetworkResetter");
            
            return Path.Combine(configDir, "config.json");
        }

        private static bool IsFileSecure(FileInfo fileInfo)
        {
            try
            {
                // 检查文件是否只有当前用户可以访问
                var accessControl = fileInfo.GetAccessControl();
                var rules = accessControl.GetAccessRules(true, true, typeof(System.Security.Principal.SecurityIdentifier));
                
                // 简单的安全检查 - 在生产环境中应该更严格
                return fileInfo.Length < 1024 * 1024; // 限制配置文件大小为1MB
            }
            catch
            {
                return false;
            }
        }

        private static bool ValidateConfig(AppConfig config)
        {
            if (config == null) return false;
            
            // 验证配置值的合理性
            if (config.Security?.CommandTimeoutMs <= 0 || config.Security.CommandTimeoutMs > 300000) // 最大5分钟
                return false;
                
            if (config.Logging?.MaxLogFileSizeMB <= 0 || config.Logging.MaxLogFileSizeMB > 100) // 最大100MB
                return false;
                
            if (config.Logging?.MaxLogFiles <= 0 || config.Logging.MaxLogFiles > 50) // 最大50个文件
                return false;
                
            return true;
        }

        public void SaveConfig()
        {
            try
            {
                var configPath = GetConfigPath();
                var configDir = Path.GetDirectoryName(configPath);
                
                // 确保目录存在
                Directory.CreateDirectory(configDir);
                
                // 验证配置
                if (!ValidateConfig(this))
                {
                    throw new InvalidOperationException("配置验证失败，无法保存");
                }
                
                var json = JsonSerializer.Serialize(this, new JsonSerializerOptions 
                { 
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });
                
                // 原子写入 - 先写入临时文件，然后重命名
                var tempPath = configPath + ".tmp";
                File.WriteAllText(tempPath, json);
                
                // 设置文件权限（仅当前用户可访问）
                var fileInfo = new FileInfo(tempPath);
                var accessControl = fileInfo.GetAccessControl();
                accessControl.SetAccessRuleProtection(true, false); // 禁用继承
                
                // 只给当前用户完全控制权限
                var currentUser = System.Security.Principal.WindowsIdentity.GetCurrent();
                var accessRule = new System.Security.AccessControl.FileSystemAccessRule(
                    currentUser.User,
                    System.Security.AccessControl.FileSystemRights.FullControl,
                    System.Security.AccessControl.AccessControlType.Allow);
                accessControl.SetAccessRule(accessRule);
                fileInfo.SetAccessControl(accessControl);
                
                // 原子替换
                if (File.Exists(configPath))
                {
                    File.Replace(tempPath, configPath, null);
                }
                else
                {
                    File.Move(tempPath, configPath);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"配置保存失败: {ex.Message}");
                throw new InvalidOperationException("配置保存失败", ex);
            }
        }
    }

    public class NetworkDiagnosticConfig
    {
        public List<string> PingHosts { get; set; } = new()
        {
            "*********",    // 阿里DNS
            "***************", // 114DNS
            "*******"       // Google DNS (可能被墙)
        };

        public List<string> DnsTestSites { get; set; } = new()
        {
            "www.baidu.com",
            "www.qq.com",
            "www.microsoft.com"
        };

        public int PingTimeoutMs { get; set; } = 5000;
        public int DnsTimeoutMs { get; set; } = 10000;
        public int MaxConcurrentPings { get; set; } = 3;
    }

    public class LoggingConfig
    {
        public string LogLevel { get; set; } = "Info";
        public int MaxLogFileSizeMB { get; set; } = 10;
        public int MaxLogFiles { get; set; } = 5;
        public bool EnableConsoleLogging { get; set; } = false;
    }

    public class SecurityConfig
    {
        public List<string> AllowedCommands { get; set; } = new()
        {
            "netsh",
            "ipconfig",
            "netstat",
            "ping"
        };

        public int CommandTimeoutMs { get; set; } = 30000;
        public bool EnableCommandValidation { get; set; } = true;
    }

    public class UIConfig
    {
        public string Theme { get; set; } = "Light";
        public string Language { get; set; } = "zh-CN";
        public bool ShowAdvancedOptions { get; set; } = false;
        public int OperationTimeoutMs { get; set; } = 60000;
    }
}