@echo off
echo 正在以管理员身份启动 NetworkResetter...
echo.

REM 检查是否已经是管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo 已具有管理员权限，直接启动应用程序...
    goto :run_app
) else (
    echo 需要管理员权限，正在请求权限提升...
    goto :request_admin
)

:request_admin
REM 请求管理员权限并重新运行
powershell -Command "Start-Process -FilePath '%~dp0NetworkResetter\bin\Release\net8.0-windows\win-x64\NetworkResetter.exe' -Verb RunAs"
goto :end

:run_app
REM 直接运行应用程序
"%~dp0NetworkResetter\bin\Release\net8.0-windows\win-x64\NetworkResetter.exe"

:end
echo.
echo 应用程序已启动或启动失败。
pause