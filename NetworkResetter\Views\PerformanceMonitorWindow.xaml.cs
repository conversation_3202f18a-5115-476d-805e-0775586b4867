using System;
using System.Diagnostics;
using System.Net.NetworkInformation;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;
using NetworkResetter.Common.Interfaces;

namespace NetworkResetter.Views
{
    public partial class PerformanceMonitorWindow : Window
    {
        private readonly ILoggingService _logger;
        private readonly DispatcherTimer _refreshTimer;
        private readonly PerformanceCounter _cpuCounter;
        private readonly Process _currentProcess;
        private readonly DateTime _startTime;

        public PerformanceMonitorWindow(ILoggingService logger)
        {
            InitializeComponent();
            _logger = logger;
            _currentProcess = Process.GetCurrentProcess();
            _startTime = DateTime.Now;

            // 初始化性能计数器
            _cpuCounter = new PerformanceCounter("Processor", "% Processor Time", "_Total");
            _cpuCounter.NextValue(); // 第一次调用返回0，需要预热

            // 设置定时器
            _refreshTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(2)
            };
            _refreshTimer.Tick += RefreshTimer_Tick;
            _refreshTimer.Start();

            // 立即刷新一次
            _ = RefreshDataAsync();
        }

        private async void RefreshTimer_Tick(object sender, EventArgs e)
        {
            if (AutoRefreshCheckBox.IsChecked == true)
            {
                await RefreshDataAsync();
            }
        }

        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            await RefreshDataAsync();
        }

        private async Task RefreshDataAsync()
        {
            try
            {
                // CPU使用率
                var cpuUsage = _cpuCounter.NextValue();
                CpuProgressBar.Value = cpuUsage;
                CpuPercentText.Text = $"{cpuUsage:F1}%";

                // 内存使用
                var totalMemory = GC.GetTotalMemory(false);
                var workingSet = _currentProcess.WorkingSet64;
                var memoryUsageMB = workingSet / (1024 * 1024);
                
                // 获取系统总内存（简化版本）
                var totalSystemMemory = GetTotalSystemMemory();
                var memoryPercent = totalSystemMemory > 0 ? (double)workingSet / totalSystemMemory * 100 : 0;
                
                MemoryProgressBar.Value = Math.Min(memoryPercent, 100);
                MemoryPercentText.Text = $"{memoryPercent:F1}%";
                MemoryDetailsText.Text = $"工作集: {memoryUsageMB}MB, GC内存: {totalMemory / (1024 * 1024)}MB";

                // 网络活动
                await UpdateNetworkInfoAsync();

                // 应用程序信息
                var uptime = DateTime.Now - _startTime;
                AppMemoryText.Text = $"内存使用: {memoryUsageMB}MB";
                AppCpuText.Text = $"CPU时间: {_currentProcess.TotalProcessorTime.TotalSeconds:F1}秒";
                AppUptimeText.Text = $"运行时间: {uptime:hh\\:mm\\:ss}";
                AppThreadsText.Text = $"线程数: {_currentProcess.Threads.Count}";

                await _logger.LogInfoAsync($"性能监控更新 - CPU: {cpuUsage:F1}%, 内存: {memoryUsageMB}MB");
            }
            catch (Exception ex)
            {
                await _logger.LogErrorAsync("性能监控更新失败", ex);
            }
        }

        private async Task UpdateNetworkInfoAsync()
        {
            try
            {
                var interfaces = NetworkInterface.GetAllNetworkInterfaces();
                long totalBytesSent = 0;
                long totalBytesReceived = 0;
                int activeConnections = 0;

                foreach (var networkInterface in interfaces)
                {
                    if (networkInterface.OperationalStatus == OperationalStatus.Up)
                    {
                        var stats = networkInterface.GetIPv4Statistics();
                        totalBytesSent += stats.BytesSent;
                        totalBytesReceived += stats.BytesReceived;
                        activeConnections++;
                    }
                }

                NetworkSentText.Text = $"发送数据: {FormatBytes(totalBytesSent)}";
                NetworkReceivedText.Text = $"接收数据: {FormatBytes(totalBytesReceived)}";
                NetworkConnectionsText.Text = $"活动接口: {activeConnections}";
            }
            catch (Exception ex)
            {
                NetworkSentText.Text = "网络信息获取失败";
                NetworkReceivedText.Text = "";
                NetworkConnectionsText.Text = "";
                await _logger.LogWarningAsync($"网络信息更新失败: {ex.Message}");
            }
        }

        private long GetTotalSystemMemory()
        {
            try
            {
                // 使用WMI获取系统内存信息
                using var searcher = new System.Management.ManagementObjectSearcher("SELECT TotalPhysicalMemory FROM Win32_ComputerSystem");
                using var collection = searcher.Get();
                
                foreach (System.Management.ManagementObject obj in collection)
                {
                    return Convert.ToInt64(obj["TotalPhysicalMemory"]);
                }
                
                return 8L * 1024 * 1024 * 1024; // 默认8GB
            }
            catch
            {
                return 8L * 1024 * 1024 * 1024; // 默认8GB
            }
        }

        private string FormatBytes(long bytes)
        {
            string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
            int counter = 0;
            decimal number = bytes;
            
            while (Math.Round(number / 1024) >= 1)
            {
                number /= 1024;
                counter++;
            }
            
            return $"{number:n1} {suffixes[counter]}";
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        protected override void OnClosed(EventArgs e)
        {
            _refreshTimer?.Stop();
            _cpuCounter?.Dispose();
            base.OnClosed(e);
        }
    }
}