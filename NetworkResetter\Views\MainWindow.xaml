<Window x:Class="NetworkResetter.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:local="clr-namespace:NetworkResetter.Converters"
        Title="网络环境重置器" Height="700" Width="900"
        WindowStartupLocation="CenterScreen"
        Closing="Window_Closing">
    
    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <local:InverseBooleanConverter x:Key="InverseBooleanConverter"/>
    </Window.Resources>
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题区域 -->
        <Grid Grid.Row="0" Margin="0,0,0,20">
            <TextBlock Text="网络环境重置器" 
                       FontSize="24" FontWeight="Bold" 
                       HorizontalAlignment="Center"/>
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Content="性能监控" 
                        Command="{Binding ShowPerformanceMonitorCommand}"
                        Width="80" Height="30" Margin="0,0,5,0"/>
                <Button Content="设置" 
                        Command="{Binding ShowSettingsCommand}"
                        Width="60" Height="30" Margin="0,0,5,0"/>
                <Button Content="帮助" 
                        Command="{Binding ShowHelpCommand}"
                        Width="60" Height="30"/>
            </StackPanel>
        </Grid>

        <!-- 主要功能区域 -->
        <TabControl Grid.Row="1">
            <!-- 网络重置选项卡 -->
            <TabItem Header="网络重置">
                <Grid Margin="20">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <Button Grid.Row="0" Content="重置 TCP/IP 栈" 
                            Command="{Binding ResetTcpIpCommand}"
                            IsEnabled="{Binding IsOperationInProgress, Converter={StaticResource InverseBooleanConverter}}"
                            Height="40" Margin="0,10" FontSize="14"/>

                    <Button Grid.Row="1" Content="重置网络适配器" 
                            Command="{Binding ResetAdaptersCommand}"
                            IsEnabled="{Binding IsOperationInProgress, Converter={StaticResource InverseBooleanConverter}}"
                            Height="40" Margin="0,10" FontSize="14"/>

                    <Button Grid.Row="2" Content="重置代理设置" 
                            Command="{Binding ResetProxyCommand}"
                            IsEnabled="{Binding IsOperationInProgress, Converter={StaticResource InverseBooleanConverter}}"
                            Height="40" Margin="0,10" FontSize="14"/>

                    <Button Grid.Row="3" Content="重置防火墙设置" 
                            Command="{Binding ResetFirewallCommand}"
                            IsEnabled="{Binding IsOperationInProgress, Converter={StaticResource InverseBooleanConverter}}"
                            Height="40" Margin="0,10" FontSize="14"/>

                    <!-- 诊断结果显示 -->
                    <GroupBox Grid.Row="4" Header="网络诊断与操作" Margin="0,20,0,0">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>
                            
                            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="10">
                                <Button Content="运行网络诊断" 
                                        Command="{Binding RunDiagnosticsCommand}"
                                        IsEnabled="{Binding IsOperationInProgress, Converter={StaticResource InverseBooleanConverter}}"
                                        Width="120" Height="30" Margin="0,0,10,0"/>
                                <Button Content="查看网络配置" 
                                        Command="{Binding ViewNetworkConfigCommand}"
                                        Width="120" Height="30" Margin="0,0,10,0"/>
                                <Button Content="系统健康检查" 
                                        Command="{Binding RunHealthCheckCommand}"
                                        IsEnabled="{Binding IsOperationInProgress, Converter={StaticResource InverseBooleanConverter}}"
                                        Width="120" Height="30" Margin="0,0,10,0"/>
                                <Button Content="完全重置" 
                                        Command="{Binding ResetAllCommand}"
                                        IsEnabled="{Binding IsOperationInProgress, Converter={StaticResource InverseBooleanConverter}}"
                                        Width="120" Height="30" 
                                        Style="{StaticResource DangerButtonStyle}"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Row="1" Margin="10" DataContext="{Binding Diagnostics}">
                                <TextBlock Text="{Binding InternetConnectivity, StringFormat='网络连通性: {0}'}" Margin="0,2"/>
                                <TextBlock Text="{Binding PingLatency, StringFormat='平均延迟: {0}ms'}" Margin="0,2"/>
                                <TextBlock Text="{Binding DnsResolution, StringFormat='DNS解析: {0}'}" Margin="0,2"/>
                                <TextBlock Text="{Binding TestTime, StringFormat='测试时间: {0:yyyy-MM-dd HH:mm:ss}'}" Margin="0,2"/>
                            </StackPanel>
                        </Grid>
                    </GroupBox>
                </Grid>
            </TabItem>

            <!-- 备份恢复选项卡 -->
            <TabItem Header="备份恢复">
                <Grid Margin="20">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <Button Grid.Row="0" Content="创建当前配置备份" 
                            Command="{Binding CreateBackupCommand}"
                            IsEnabled="{Binding IsOperationInProgress, Converter={StaticResource InverseBooleanConverter}}"
                            Height="40" Margin="0,10" FontSize="14"/>

                    <GroupBox Grid.Row="1" Header="可用备份" Margin="0,20,0,20">
                        <ListBox ItemsSource="{Binding AvailableBackups}" 
                                 SelectedItem="{Binding SelectedBackup}"
                                 Margin="10">
                            <ListBox.ItemTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding}" Margin="5"/>
                                </DataTemplate>
                            </ListBox.ItemTemplate>
                        </ListBox>
                    </GroupBox>

                    <Button Grid.Row="2" Content="恢复选中备份" 
                            Command="{Binding RestoreBackupCommand}"
                            CommandParameter="{Binding SelectedBackup}"
                            IsEnabled="{Binding IsOperationInProgress, Converter={StaticResource InverseBooleanConverter}}"
                            Height="40" FontSize="14"/>
                </Grid>
            </TabItem>

            <!-- 日志查看选项卡 -->
            <TabItem Header="操作日志">
                <Grid Margin="20">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <Button Grid.Row="0" Content="刷新日志" 
                            Command="{Binding ViewLogsCommand}"
                            Height="40" Margin="0,10" FontSize="14" HorizontalAlignment="Left" Width="120"/>

                    <ScrollViewer Grid.Row="1" Margin="0,20,0,0">
                        <TextBox Text="{Binding OperationLog}" 
                                 IsReadOnly="True" 
                                 TextWrapping="Wrap" 
                                 FontFamily="Consolas" 
                                 FontSize="12"
                                 Background="#FFF8F8F8"
                                 BorderThickness="1"
                                 BorderBrush="#FFCCCCCC"/>
                    </ScrollViewer>
                </Grid>
            </TabItem>
        </TabControl>

        <!-- 状态栏 -->
        <StatusBar Grid.Row="2" Height="30">
            <StatusBarItem>
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="状态: " FontWeight="Bold"/>
                    <TextBlock Text="{Binding StatusMessage}"/>
                    <ProgressBar Width="100" Height="16" Margin="20,0,0,0" 
                                 IsIndeterminate="{Binding IsOperationInProgress}"
                                 Visibility="{Binding IsOperationInProgress, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                </StackPanel>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>