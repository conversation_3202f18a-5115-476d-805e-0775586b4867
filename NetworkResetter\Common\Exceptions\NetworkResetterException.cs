using System;

namespace NetworkResetter.Common.Exceptions
{
    public class NetworkResetterException : Exception
    {
        public string ErrorCode { get; }
        public string UserFriendlyMessage { get; }

        public NetworkResetterException(string message, string errorCode = null, string userFriendlyMessage = null) 
            : base(message)
        {
            ErrorCode = errorCode ?? "GENERAL_ERROR";
            UserFriendlyMessage = userFriendlyMessage ?? message;
        }

        public NetworkResetterException(string message, Exception innerException, string errorCode = null, string userFriendlyMessage = null) 
            : base(message, innerException)
        {
            ErrorCode = errorCode ?? "GENERAL_ERROR";
            UserFriendlyMessage = userFriendlyMessage ?? message;
        }
    }

    public class CommandExecutionException : NetworkResetterException
    {
        public string Command { get; }
        public int ExitCode { get; }

        public CommandExecutionException(string command, int exitCode, string message) 
            : base($"命令执行失败: {command} (退出码: {exitCode}) - {message}", "COMMAND_EXECUTION_FAILED", $"命令执行失败: {message}")
        {
            Command = command;
            ExitCode = exitCode;
        }
    }

    public class SecurityException : NetworkResetterException
    {
        public SecurityException(string message) 
            : base(message, "SECURITY_VIOLATION", "安全验证失败，操作被拒绝")
        {
        }
    }

    public class ConfigurationException : NetworkResetterException
    {
        public ConfigurationException(string message) 
            : base(message, "CONFIGURATION_ERROR", "配置错误，请检查应用程序设置")
        {
        }
    }
}