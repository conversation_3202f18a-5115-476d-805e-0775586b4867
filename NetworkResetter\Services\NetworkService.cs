using System;
using System.Collections.Generic;
using System.Linq;
using System.Management;
using System.Net.NetworkInformation;
using System.Threading;
using System.Threading.Tasks;
using NetworkResetter.Common.Configuration;
using NetworkResetter.Common.Exceptions;
using NetworkResetter.Common.Interfaces;
using NetworkResetter.Common.Services;
using NetworkResetter.Models;

namespace NetworkResetter.Services
{
    public class NetworkService : INetworkService
    {
        private readonly ILoggingService _logger;
        private readonly ISystemCommandService _commandService;
        private readonly AppConfig _config;
        private readonly SemaphoreSlim _operationSemaphore;

        public NetworkService(ILoggingService logger, ISystemCommandService commandService)
        {
            _logger = logger;
            _commandService = commandService;
            _config = AppConfig.Instance;
            _operationSemaphore = new SemaphoreSlim(1, 1);
        }
        public async Task<OperationResult> ResetTcpIpStackAsync()
        {
            await _operationSemaphore.WaitAsync();
            try
            {
                await _logger.LogInfoAsync("开始重置TCP/IP栈");
                
                var commands = new[]
                {
                    "netsh winsock reset",
                    "netsh int ip reset",
                    "ipconfig /release",
                    "ipconfig /renew", 
                    "ipconfig /flushdns",
                    "netsh int ipv4 reset reset.log",
                    "netsh int ipv6 reset reset.log"
                };

                var result = OperationResult.CreateSuccess("TCP/IP栈重置完成");
                var failedCommands = new List<string>();

                foreach (var command in commands)
                {
                    var commandResult = await _commandService.ExecuteCommandSafeAsync(
                        command, _config.Security.AllowedCommands.ToArray(), _config.Security.CommandTimeoutMs);
                    
                    if (!commandResult.Success)
                    {
                        failedCommands.Add($"{command}: {commandResult.Error}");
                        await _logger.LogWarningAsync($"命令执行失败: {command} - {commandResult.Error}");
                    }
                    else
                    {
                        result.AddDetail($"✓ {command}");
                    }
                }

                if (failedCommands.Any())
                {
                    result.Success = failedCommands.Count < commands.Length; // 部分成功
                    result.Message = failedCommands.Count == commands.Length ? 
                        "TCP/IP栈重置失败" : "TCP/IP栈重置部分完成";
                    
                    foreach (var failed in failedCommands)
                    {
                        result.AddDetail($"✗ {failed}");
                    }
                }
                
                await _logger.LogInfoAsync($"TCP/IP栈重置操作完成，成功: {result.Success}");
                return result;
            }
            catch (Exception ex)
            {
                await _logger.LogErrorAsync("重置TCP/IP栈异常", ex);
                return OperationResult.CreateFailure("TCP/IP栈重置异常", "TCPIP_RESET_EXCEPTION", ex);
            }
            finally
            {
                _operationSemaphore.Release();
            }
        }

        public async Task<OperationResult> ResetNetworkAdaptersAsync()
        {
            await _operationSemaphore.WaitAsync();
            try
            {
                await _logger.LogInfoAsync("开始重置网络适配器");
                
                var result = OperationResult.CreateSuccess("网络适配器重置完成");
                var adapters = new List<ManagementObject>();
                
                // 使用using确保资源释放
                using (var searcher = new ManagementObjectSearcher(
                    "SELECT * FROM Win32_NetworkAdapter WHERE NetEnabled=true AND AdapterTypeId=0"))
                {
                    using (var collection = searcher.Get())
                    {
                        adapters.AddRange(collection.Cast<ManagementObject>());
                    }
                }
                
                await _logger.LogInfoAsync($"找到 {adapters.Count} 个网络适配器");

                var successCount = 0;
                var failedAdapters = new List<string>();

                foreach (var adapter in adapters)
                {
                    var adapterName = "未知适配器";
                    try
                    {
                        adapterName = adapter["Name"]?.ToString() ?? "未知适配器";
                        await _logger.LogInfoAsync($"重置适配器: {adapterName}");
                        
                        // 禁用适配器
                        var disableResult = adapter.InvokeMethod("Disable", null) as ManagementBaseObject;
                        if (disableResult != null && (uint)disableResult["ReturnValue"] != 0)
                        {
                            throw new InvalidOperationException($"禁用适配器失败，返回码: {disableResult["ReturnValue"]}");
                        }
                        
                        await Task.Delay(3000);
                        
                        // 启用适配器
                        var enableResult = adapter.InvokeMethod("Enable", null) as ManagementBaseObject;
                        if (enableResult != null && (uint)enableResult["ReturnValue"] != 0)
                        {
                            throw new InvalidOperationException($"启用适配器失败，返回码: {enableResult["ReturnValue"]}");
                        }
                        
                        await Task.Delay(2000);
                        
                        successCount++;
                        result.AddDetail($"✓ {adapterName}");
                        await _logger.LogInfoAsync($"适配器 {adapterName} 重置完成");
                    }
                    catch (Exception adapterEx)
                    {
                        failedAdapters.Add(adapterName);
                        result.AddDetail($"✗ {adapterName}: {adapterEx.Message}");
                        await _logger.LogWarningAsync($"适配器 {adapterName} 重置失败: {adapterEx.Message}");
                    }
                    finally
                    {
                        // 确保ManagementObject被释放
                        adapter?.Dispose();
                    }
                }

                if (failedAdapters.Any())
                {
                    result.Success = successCount > 0; // 至少有一个成功
                    result.Message = successCount == 0 ? 
                        "网络适配器重置失败" : $"网络适配器重置部分完成 ({successCount}/{adapters.Count})";
                }
                
                await _logger.LogInfoAsync($"网络适配器重置操作完成，成功: {successCount}/{adapters.Count}");
                return result;
            }
            catch (Exception ex)
            {
                await _logger.LogErrorAsync("重置网络适配器异常", ex);
                return OperationResult.CreateFailure("网络适配器重置异常", "ADAPTER_RESET_EXCEPTION", ex);
            }
            finally
            {
                _operationSemaphore.Release();
            }
        }

        public async Task<NetworkDiagnostics> RunDiagnosticsAsync()
        {
            var diagnostics = new NetworkDiagnostics
            {
                TestTime = DateTime.Now
            };

            try
            {
                await _logger.LogInfoAsync("开始网络诊断");

                // 并发测试多个DNS服务器的连通性
                var pingTasks = _config.NetworkDiagnostic.PingHosts
                    .Select(host => TestPingAsync(host, _config.NetworkDiagnostic.PingTimeoutMs))
                    .ToArray();

                var pingResults = await Task.WhenAll(pingTasks);
                var successfulPings = pingResults.Where(r => r.Success).ToList();

                diagnostics.InternetConnectivity = successfulPings.Any();
                diagnostics.PingLatency = successfulPings.Any() ? 
                    (int)successfulPings.Average(r => r.Latency) : -1;

                // 记录详细的ping结果
                foreach (var result in pingResults)
                {
                    if (result.Success)
                    {
                        await _logger.LogInfoAsync($"Ping {result.Host}: {result.Latency}ms");
                    }
                    else
                    {
                        await _logger.LogWarningAsync($"Ping {result.Host} 失败: {result.Error}");
                    }
                }

                // 并发DNS解析测试
                var dnsTasks = _config.NetworkDiagnostic.DnsTestSites
                    .Select(site => TestDnsResolutionAsync(site, _config.NetworkDiagnostic.DnsTimeoutMs))
                    .ToArray();

                var dnsResults = await Task.WhenAll(dnsTasks);
                var successfulDns = dnsResults.Where(r => r.Success).ToList();

                diagnostics.DnsResolution = successfulDns.Any() ? "正常" : "失败";

                // 记录详细的DNS结果
                foreach (var result in dnsResults)
                {
                    if (result.Success)
                    {
                        await _logger.LogInfoAsync($"DNS解析 {result.Host} 成功，耗时: {result.ResponseTime}ms");
                    }
                    else
                    {
                        await _logger.LogWarningAsync($"DNS解析 {result.Host} 失败: {result.Error}");
                    }
                }

                // 获取活动连接信息
                diagnostics.ActiveConnections = await GetActiveConnectionsAsync();

                await _logger.LogInfoAsync($"网络诊断完成 - 连通性: {diagnostics.InternetConnectivity}, 平均延迟: {diagnostics.PingLatency}ms, DNS: {diagnostics.DnsResolution}");
                return diagnostics;
            }
            catch (Exception ex)
            {
                await _logger.LogErrorAsync("网络诊断异常", ex);
                return diagnostics;
            }
        }

        private async Task<PingResult> TestPingAsync(string host, int timeoutMs)
        {
            try
            {
                using var ping = new Ping();
                var reply = await ping.SendPingAsync(host, timeoutMs);
                
                return new PingResult
                {
                    Host = host,
                    Success = reply.Status == IPStatus.Success,
                    Latency = reply.Status == IPStatus.Success ? reply.RoundtripTime : 0,
                    Error = reply.Status != IPStatus.Success ? reply.Status.ToString() : null
                };
            }
            catch (Exception ex)
            {
                return new PingResult
                {
                    Host = host,
                    Success = false,
                    Error = ex.Message
                };
            }
        }

        private async Task<DnsResult> TestDnsResolutionAsync(string hostname, int timeoutMs)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            try
            {
                using var cts = new CancellationTokenSource(timeoutMs);
                var hostEntry = await System.Net.Dns.GetHostEntryAsync(hostname).WaitAsync(cts.Token);
                stopwatch.Stop();
                
                return new DnsResult
                {
                    Host = hostname,
                    Success = true,
                    ResponseTime = stopwatch.ElapsedMilliseconds,
                    IpAddresses = hostEntry.AddressList.Select(ip => ip.ToString()).ToList()
                };
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                return new DnsResult
                {
                    Host = hostname,
                    Success = false,
                    ResponseTime = stopwatch.ElapsedMilliseconds,
                    Error = ex.Message
                };
            }
        }

        private class PingResult
        {
            public string Host { get; set; }
            public bool Success { get; set; }
            public long Latency { get; set; }
            public string Error { get; set; }
        }

        private class DnsResult
        {
            public string Host { get; set; }
            public bool Success { get; set; }
            public long ResponseTime { get; set; }
            public List<string> IpAddresses { get; set; } = new();
            public string Error { get; set; }
        }

        public async Task<List<NetworkConfig>> GetNetworkConfigsAsync()
        {
            var configs = new List<NetworkConfig>();
            
            try
            {
                await _logger.LogInfoAsync("获取网络配置信息");
                
                using var searcher = new ManagementObjectSearcher(
                    "SELECT * FROM Win32_NetworkAdapterConfiguration WHERE IPEnabled=true");
                
                using var collection = searcher.Get();
                
                foreach (ManagementObject config in collection)
                {
                    try
                    {
                        var networkConfig = new NetworkConfig
                        {
                            AdapterName = config["Description"]?.ToString() ?? "未知",
                            DhcpEnabled = Convert.ToBoolean(config["DHCPEnabled"] ?? false),
                            BackupTime = DateTime.Now
                        };

                        // 安全地获取IP地址
                        if (config["IPAddress"] is string[] ipAddresses && ipAddresses.Length > 0)
                        {
                            networkConfig.IpAddress = ipAddresses[0];
                        }

                        // 安全地获取子网掩码
                        if (config["IPSubnet"] is string[] subnets && subnets.Length > 0)
                        {
                            networkConfig.SubnetMask = subnets[0];
                        }

                        // 安全地获取网关
                        if (config["DefaultIPGateway"] is string[] gateways && gateways.Length > 0)
                        {
                            networkConfig.Gateway = gateways[0];
                        }

                        // 安全地获取DNS服务器
                        if (config["DNSServerSearchOrder"] is string[] dnsServers)
                        {
                            networkConfig.DnsServers.AddRange(dnsServers);
                        }

                        // 获取更多详细信息
                        networkConfig.MacAddress = config["MACAddress"]?.ToString();
                        networkConfig.DhcpServer = config["DHCPServer"]?.ToString();
                        
                        if (config["DHCPLeaseObtained"] != null && 
                            DateTime.TryParse(config["DHCPLeaseObtained"].ToString(), out var leaseObtained))
                        {
                            networkConfig.DhcpLeaseObtained = leaseObtained;
                        }

                        if (config["DHCPLeaseExpires"] != null && 
                            DateTime.TryParse(config["DHCPLeaseExpires"].ToString(), out var leaseExpires))
                        {
                            networkConfig.DhcpLeaseExpires = leaseExpires;
                        }

                        configs.Add(networkConfig);
                    }
                    catch (Exception configEx)
                    {
                        await _logger.LogWarningAsync($"获取网络配置项失败: {configEx.Message}");
                    }
                    finally
                    {
                        config?.Dispose();
                    }
                }

                await _logger.LogInfoAsync($"获取到 {configs.Count} 个网络配置");
            }
            catch (Exception ex)
            {
                await _logger.LogErrorAsync("获取网络配置失败", ex);
            }

            return configs;
        }

        private async Task<List<string>> GetActiveConnectionsAsync()
        {
            var connections = new List<string>();
            
            try
            {
                var commandResult = await _commandService.ExecuteCommandSafeAsync(
                    "netstat -an", _config.Security.AllowedCommands.ToArray());
                
                if (commandResult.Success)
                {
                    var lines = commandResult.Output.Split('\n', StringSplitOptions.RemoveEmptyEntries);
                    
                    foreach (var line in lines)
                    {
                        var trimmedLine = line.Trim();
                        if (trimmedLine.Contains("ESTABLISHED") || trimmedLine.Contains("LISTENING"))
                        {
                            // 解析连接信息，提取有用部分
                            var parts = trimmedLine.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
                            if (parts.Length >= 4)
                            {
                                var protocol = parts[0];
                                var localAddress = parts[1];
                                var remoteAddress = parts[2];
                                var state = parts[3];
                                
                                connections.Add($"{protocol} {localAddress} -> {remoteAddress} [{state}]");
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                await _logger.LogWarningAsync($"获取活动连接失败: {ex.Message}");
            }

            // 限制显示前20个连接，避免界面过于拥挤
            return connections.Take(20).ToList();
        }
    }
}