using System;
using System.Collections.Generic;

namespace NetworkResetter.Models
{
    public class OperationResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public string ErrorCode { get; set; }
        public Exception Exception { get; set; }
        public List<string> Details { get; set; } = new();

        public static OperationResult CreateSuccess(string message = "操作成功")
        {
            return new OperationResult
            {
                Success = true,
                Message = message
            };
        }

        public static OperationResult CreateFailure(string message, string errorCode = null, Exception exception = null)
        {
            return new OperationResult
            {
                Success = false,
                Message = message,
                ErrorCode = errorCode,
                Exception = exception
            };
        }

        public OperationResult AddDetail(string detail)
        {
            Details.Add(detail);
            return this;
        }
    }

    public class OperationResult<T> : OperationResult
    {
        public T Data { get; set; }

        public static OperationResult<T> CreateSuccess(T data, string message = "操作成功")
        {
            return new OperationResult<T>
            {
                Success = true,
                Message = message,
                Data = data
            };
        }

        public static new OperationResult<T> CreateFailure(string message, string errorCode = null, Exception exception = null)
        {
            return new OperationResult<T>
            {
                Success = false,
                Message = message,
                ErrorCode = errorCode,
                Exception = exception
            };
        }

        public new OperationResult<T> AddDetail(string detail)
        {
            Details.Add(detail);
            return this;
        }
    }

    public class CommandResult
    {
        public bool Success { get; set; }
        public string Output { get; set; }
        public string Error { get; set; }
        public int ExitCode { get; set; }
        public TimeSpan ExecutionTime { get; set; }
    }
}